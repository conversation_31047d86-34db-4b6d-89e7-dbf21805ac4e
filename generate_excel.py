#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成Excel文件，包含原子表和组件表
第一个sheet：原子表 - 包含所有原子功能的id、name、description
第二个sheet：组件表 - 包含所有组件的信息
"""

import json
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"错误：JSON解析失败 {e}")
        return None

def extract_atomic_functions(atomic_data, architecture_data=None):
    """从原子功能JSON中提取所有原子功能，并补充架构文件中的信息"""
    atomic_functions = []

    if not atomic_data or 'complete_atomic_functions_list' not in atomic_data:
        print("错误：原子功能数据格式不正确")
        return atomic_functions

    # 提取原始原子功能
    atomic_by_category = atomic_data['complete_atomic_functions_list'].get('atomic_functions_by_category', {})
    for category, category_data in atomic_by_category.items():
        functions = category_data.get('functions', [])
        for func in functions:
            atomic_functions.append({
                'id': func.get('id', ''),
                'name': func.get('name', ''),
                'description': func.get('description', ''),
                'category': category,
                'type': '原始功能'
            })

    # 提取补充原子功能
    supplementary_functions = atomic_data['complete_atomic_functions_list'].get('supplementary_functions', {})
    for category, category_data in supplementary_functions.items():
        functions = category_data.get('functions', [])
        for func in functions:
            atomic_functions.append({
                'id': func.get('id', ''),
                'name': func.get('name', ''),
                'description': func.get('description', ''),
                'category': f"supplementary_{category}",
                'type': '补充功能'
            })

    # 如果提供了架构数据，尝试从中提取额外的原子功能信息
    if architecture_data and 'complete_system_architecture' in architecture_data:
        arch_atomic_functions = architecture_data['complete_system_architecture'].get('atomic_functions', {})
        for category, functions in arch_atomic_functions.items():
            if isinstance(functions, list):
                for func in functions:
                    # 检查是否已经存在
                    existing_ids = [af['id'] for af in atomic_functions]
                    if func.get('atomic_id', '') not in existing_ids:
                        atomic_functions.append({
                            'id': func.get('atomic_id', ''),
                            'name': func.get('atomic_name', ''),
                            'description': func.get('atomic_description', ''),
                            'category': f"arch_{category}",
                            'type': '架构详细'
                        })

    return atomic_functions

def extract_components(architecture_data):
    """从系统架构JSON中提取所有组件"""
    components = []
    
    if not architecture_data or 'complete_system_architecture' not in architecture_data:
        print("错误：系统架构数据格式不正确")
        return components
    
    component_templates = architecture_data['complete_system_architecture'].get('component_templates', {})
    
    # 提取core_architecture中的组件
    core_architecture = component_templates.get('core_architecture', [])
    for component in core_architecture:
        components.append({
            'component_id': component.get('component_id', ''),
            'component_name': component.get('component_name', ''),
            'component_description': component.get('component_description', ''),
            'atomic_function_ids': ', '.join(component.get('atomic_function_ids', [])),
            'category': 'core_architecture'
        })
    
    # 提取其他类别的组件
    for category, category_components in component_templates.items():
        if category != 'core_architecture' and isinstance(category_components, list):
            for component in category_components:
                components.append({
                    'component_id': component.get('component_id', ''),
                    'component_name': component.get('component_name', ''),
                    'component_description': component.get('component_description', ''),
                    'atomic_function_ids': ', '.join(component.get('atomic_function_ids', [])),
                    'category': category
                })
    
    return components

def style_worksheet(ws, title):
    """设置工作表样式"""
    # 设置标题样式
    title_font = Font(name='微软雅黑', size=14, bold=True, color='FFFFFF')
    title_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    title_alignment = Alignment(horizontal='center', vertical='center')
    
    # 设置边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 应用标题样式
    for cell in ws[1]:
        cell.font = title_font
        cell.fill = title_fill
        cell.alignment = title_alignment
        cell.border = thin_border
    
    # 设置数据行样式
    data_font = Font(name='微软雅黑', size=10)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    for row in ws.iter_rows(min_row=2):
        for cell in row:
            cell.font = data_font
            cell.alignment = data_alignment
            cell.border = thin_border
    
    # 自动调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)  # 最大宽度限制为50
        ws.column_dimensions[column_letter].width = adjusted_width

def create_excel_file(atomic_functions, components, output_file):
    """创建Excel文件"""
    wb = Workbook()
    
    # 删除默认工作表
    wb.remove(wb.active)
    
    # 创建原子表工作表
    atomic_ws = wb.create_sheet(title="原子表")
    atomic_df = pd.DataFrame(atomic_functions)
    
    # 重新排列列顺序
    atomic_df = atomic_df[['id', 'name', 'description', 'category', 'type']]
    atomic_df.columns = ['原子功能ID', '原子功能名称', '功能描述', '所属类别', '功能类型']
    
    # 将数据写入工作表
    for r in dataframe_to_rows(atomic_df, index=False, header=True):
        atomic_ws.append(r)
    
    # 设置样式
    style_worksheet(atomic_ws, "原子功能表")
    
    # 创建组件表工作表
    component_ws = wb.create_sheet(title="组件表")
    component_df = pd.DataFrame(components)
    
    # 重新排列列顺序
    component_df = component_df[['component_id', 'component_name', 'component_description', 'atomic_function_ids', 'category']]
    component_df.columns = ['组件ID', '组件名称', '组件描述', '包含的原子功能ID', '所属类别']
    
    # 将数据写入工作表
    for r in dataframe_to_rows(component_df, index=False, header=True):
        component_ws.append(r)
    
    # 设置样式
    style_worksheet(component_ws, "组件表")
    
    # 保存文件
    wb.save(output_file)
    print(f"Excel文件已生成：{output_file}")

def main():
    """主函数"""
    # 文件路径
    atomic_file = "complete_atomic_functions_list.json"
    architecture_file = "complete_system_architecture.json"
    output_file = "系统架构表.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(atomic_file):
        print(f"错误：找不到文件 {atomic_file}")
        return
    
    if not os.path.exists(architecture_file):
        print(f"错误：找不到文件 {architecture_file}")
        return
    
    # 加载数据
    print("正在加载原子功能数据...")
    atomic_data = load_json_file(atomic_file)
    if not atomic_data:
        return
    
    print("正在加载系统架构数据...")
    architecture_data = load_json_file(architecture_file)
    if not architecture_data:
        return
    
    # 提取数据
    print("正在提取原子功能...")
    atomic_functions = extract_atomic_functions(atomic_data, architecture_data)
    print(f"提取到 {len(atomic_functions)} 个原子功能")
    
    print("正在提取组件信息...")
    components = extract_components(architecture_data)
    print(f"提取到 {len(components)} 个组件")
    
    # 生成Excel文件
    print("正在生成Excel文件...")
    create_excel_file(atomic_functions, components, output_file)
    
    print("完成！")

if __name__ == "__main__":
    main()
