# 系统架构表.xlsx 文件说明

## 概述
本Excel文件包含了Linux远程控制工具的完整系统架构信息，分为两个工作表：

## 工作表1：原子表
包含所有原子功能的详细信息，**按原子功能ID数字顺序排列**

### 列说明：
- **原子功能ID**: 唯一标识符，格式为URC_AT001-URC_AT344（按数字顺序排列）
- **原子功能名称**: 功能的中文名称
- **功能描述**: 功能的详细描述
- **所属类别**: 功能所属的分类（如program_management、ssl_server等）
- **功能类型**: 区分原始功能和补充功能

### 数据统计：
- 总计：200个原子功能
- 原始功能：152个
- 补充功能：48个

### 主要功能类别：
1. **程序管理** (program_management) - 8个功能
2. **SSL服务器** (ssl_server) - 8个功能
3. **客户端管理** (client_management) - 8个功能
4. **心跳监控** (heartbeat_monitoring) - 8个功能
5. **命令处理** (command_processing) - 8个功能
6. **认证管理** (authentication_management) - 8个功能
7. **TLS加密通道** (tls_encryption_channel) - 8个功能
8. **TCP连接** (tcp_connection) - 8个功能
9. **消息协议** (message_protocol) - 8个功能
10. **SSL客户端** (ssl_client) - 8个功能
11. **命令执行器** (command_executor) - 8个功能
12. **文件处理器** (file_handler) - 8个功能
13. **系统信息收集器** (system_info_collector) - 8个功能
14. **ELF加载器** (elf_loader) - 8个功能
15. **日志管理** (log_manager) - 8个功能
16. **加密解密** (cryptography) - 8个功能
17. **文件传输** (file_transfer) - 8个功能
18. **进程管理** (process_manager) - 8个功能
19. **插件管理** (plugin_manager) - 8个功能

### 补充功能类别：
1. **端口复用** (supplementary_port_multiplexing) - 8个功能
2. **交互式终端** (supplementary_interactive_terminal) - 8个功能
3. **Web管理** (supplementary_web_management) - 8个功能
4. **热升级** (supplementary_hot_upgrade) - 8个功能
5. **精确日志管理** (supplementary_precise_log_management) - 8个功能
6. **增强持久化** (supplementary_enhanced_persistence) - 8个功能

## 工作表2：组件表
包含所有系统组件的详细信息，**按组件ID数字顺序排列**

### 列说明：
- **组件ID**: 唯一标识符，格式为URC_CT001-URC_CT042（按数字顺序排列）
- **组件名称**: 组件的中文名称
- **组件描述**: 组件的功能描述
- **包含的原子功能ID**: 该组件包含的所有原子功能ID列表
- **所属类别**: 组件所属的架构分类

### 数据统计：
- 总计：42个组件
- 核心架构组件：3个
- 其他分类组件：39个

### 主要组件类别：
1. **核心架构** (core_architecture) - 3个组件
2. **网络通信** (network_communication) - 4个组件
3. **认证安全** (authentication_security) - 2个组件
4. **命令执行** (command_execution) - 3个组件
5. **文件操作** (file_operations) - 2个组件
6. **系统监控** (system_monitoring) - 2个组件
7. **动态加载** (dynamic_loading) - 2个组件
8. **监控日志** (monitoring_logging) - 3个组件
9. **网络代理** (network_proxy) - 2个组件
10. **HTTP通信** (http_communication) - 2个组件
11. **高级安全** (advanced_security) - 4个组件
12. **系统控制** (system_control) - 4个组件
13. **数据收集** (data_collection) - 3个组件
14. **横向移动** (lateral_movement) - 3个组件
15. **数据渗透** (data_exfiltration) - 3个组件

## 数据来源
- **原子功能数据**: complete_atomic_functions_list.json
- **组件架构数据**: complete_system_architecture.json

## 生成工具
- **生成脚本**: generate_excel.py
- **验证脚本**: verify_excel.py
- **排序测试脚本**: test_sorting.py

## 使用说明
1. 使用Excel或其他电子表格软件打开文件
2. 可以使用筛选功能按类别查看特定的原子功能或组件
3. 可以使用查找功能快速定位特定的功能或组件
4. 建议使用冻结首行功能以便在滚动时保持标题可见

## 注意事项
- 所有数据均来源于JSON配置文件
- 原子功能ID和组件ID具有唯一性
- 每个组件都明确列出了其包含的原子功能
- 功能类型字段帮助区分原始功能和后续补充的功能
- **数据已按ID数字顺序排列，便于查找和管理**

## 更新说明
如需更新Excel文件，请：
1. 修改相应的JSON源文件
2. 重新运行generate_excel.py脚本
3. 使用verify_excel.py验证生成结果
