#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的Excel文件内容
"""

import pandas as pd
import os

def verify_excel_file(excel_file):
    """验证Excel文件内容"""
    if not os.path.exists(excel_file):
        print(f"错误：找不到文件 {excel_file}")
        return
    
    try:
        # 读取Excel文件的所有工作表
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        print(f"Excel文件 '{excel_file}' 验证结果：")
        print("=" * 50)
        
        # 检查工作表
        sheet_names = list(excel_data.keys())
        print(f"工作表数量：{len(sheet_names)}")
        print(f"工作表名称：{sheet_names}")
        print()
        
        # 验证原子表
        if "原子表" in excel_data:
            atomic_df = excel_data["原子表"]
            print("原子表信息：")
            print(f"  - 行数：{len(atomic_df)}")
            print(f"  - 列数：{len(atomic_df.columns)}")
            print(f"  - 列名：{list(atomic_df.columns)}")

            # 统计功能类型
            if '功能类型' in atomic_df.columns:
                type_counts = atomic_df['功能类型'].value_counts()
                print(f"  - 功能类型统计：")
                for func_type, count in type_counts.items():
                    print(f"    {func_type}: {count}个")

            print(f"  - 前5行数据：")
            print(atomic_df.head().to_string(index=False))
            print()
        else:
            print("警告：未找到'原子表'工作表")
        
        # 验证组件表
        if "组件表" in excel_data:
            component_df = excel_data["组件表"]
            print("组件表信息：")
            print(f"  - 行数：{len(component_df)}")
            print(f"  - 列数：{len(component_df.columns)}")
            print(f"  - 列名：{list(component_df.columns)}")
            print(f"  - 前5行数据：")
            print(component_df.head().to_string(index=False))
            print()
        else:
            print("警告：未找到'组件表'工作表")
        
        print("验证完成！")
        
    except Exception as e:
        print(f"读取Excel文件时出错：{e}")

def main():
    """主函数"""
    excel_file = "系统架构表.xlsx"
    verify_excel_file(excel_file)

if __name__ == "__main__":
    main()
