<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT039 Web管理界面组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT039 Web管理界面组件</h1>
            <p><strong>功能分类：</strong>HTTP通信 (HTTP Communication)</p>
            <p><strong>组件描述：</strong>Web界面、API服务和用户交互</p>
            <p><strong>原子函数范围：</strong>URC_AT313-URC_AT320</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[HTTP通信<br/>HTTP Communication]
    
    %% 组件
    URC_CT039[URC_CT039<br/>Web管理界面组件<br/>Web界面、API服务和用户交互]
    
    %% 原子函数
    AT313[URC_AT313<br/>HTTP服务器<br/>启动内置HTTP服务器]
    AT314[URC_AT314<br/>静态资源服务<br/>提供静态文件服务]
    AT315[URC_AT315<br/>API接口处理<br/>处理REST API请求]
    AT316[URC_AT316<br/>用户认证<br/>Web界面用户认证]
    AT317[URC_AT317<br/>会话管理<br/>管理Web会话状态]
    AT318[URC_AT318<br/>实时状态更新<br/>提供实时状态数据]
    AT319[URC_AT319<br/>文件管理界面<br/>渲染文件管理界面]
    AT320[URC_AT320<br/>系统监控界面<br/>渲染系统监控界面]
    
    %% 关系连接
    CAT --> URC_CT039
    URC_CT039 --> AT313
    URC_CT039 --> AT314
    URC_CT039 --> AT315
    URC_CT039 --> AT316
    URC_CT039 --> AT317
    URC_CT039 --> AT318
    URC_CT039 --> AT319
    URC_CT039 --> AT320
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT039 componentStyle
    class AT313,AT314,AT315,AT316,AT317,AT318,AT319,AT320 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>