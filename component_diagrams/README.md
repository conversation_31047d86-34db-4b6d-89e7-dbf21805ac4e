# 组件详细关系图

本文件夹包含42个组件的详细关系图，每个图展示了组件与其包含的8个原子函数之间的关系。

## 图表说明

- **蓝色节点**：功能分类
- **紫色节点**：组件
- **绿色节点**：原子函数
- **布局方向**：从左到右

## 组件列表

### 核心架构 (Core Architecture)
1. URC_CT001_主程序入口组件.html
2. URC_CT002_SSL服务器组件.html
3. URC_CT003_客户端管理器组件.html

### 网络通信 (Network Communication)
4. URC_CT004_TLS加密通道组件.html
5. URC_CT005_TCP连接组件.html
6. URC_CT006_消息协议组件.html
7. URC_CT037_端口复用组件.html

### 认证安全 (Authentication Security)
8. URC_CT007_认证管理器组件.html
9. URC_CT008_加密解密组件.html

### 命令执行 (Command Execution)
10. URC_CT009_命令处理器组件.html
11. URC_CT010_命令执行器组件.html
12. URC_CT038_交互式终端组件.html

### 文件操作 (File Operations)
13. URC_CT011_文件处理器组件.html
14. URC_CT012_文件传输组件.html

### 系统监控 (System Monitoring)
15. URC_CT013_系统信息收集器组件.html
16. URC_CT014_进程管理组件.html

### 动态加载 (Dynamic Loading)
17. URC_CT015_ELF加载器组件.html
18. URC_CT016_插件管理组件.html

### 监控日志 (Monitoring Logging)
19. URC_CT017_日志管理组件.html
20. URC_CT018_心跳监控组件.html
21. URC_CT041_精确日志管理组件.html

### 网络代理 (Network Proxy)
22. URC_CT019_SOCKS代理组件.html
23. URC_CT020_端口转发组件.html

### HTTP通信 (HTTP Communication)
24. URC_CT021_HTTP通信模拟组件.html
25. URC_CT039_Web管理界面组件.html

### 高级安全 (Advanced Security)
26. URC_CT022_反调试检测组件.html
27. URC_CT023_持久化机制组件.html
28. URC_CT024_网络隐蔽组件.html
29. URC_CT042_持久化增强组件.html

### 系统控制 (System Control)
30. URC_CT025_权限提升组件.html
31. URC_CT026_系统监控组件.html
32. URC_CT027_注册表操作组件.html
33. URC_CT040_热升级组件.html

### 数据收集 (Data Collection)
34. URC_CT028_键盘记录组件.html
35. URC_CT029_屏幕捕获组件.html
36. URC_CT030_网络嗅探组件.html

### 横向移动 (Lateral Movement)
37. URC_CT031_网络扫描组件.html
38. URC_CT032_凭据窃取组件.html
39. URC_CT033_远程执行组件.html

### 数据外泄 (Data Exfiltration)
40. URC_CT034_数据搜索组件.html
41. URC_CT035_数据压缩组件.html
42. URC_CT036_隐蔽传输组件.html

## 使用方法

1. 打开 `components_index.html` 查看所有组件的索引页面
2. 点击任意组件卡片查看详细的组件与原子函数关系图
3. 图表支持缩放和拖拽
4. 可以点击节点查看详细信息
5. 使用"返回组件列表"按钮回到索引页面

## 快速开始

直接在浏览器中打开：`components_index.html`
