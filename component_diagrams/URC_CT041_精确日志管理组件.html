<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT041 精确日志管理组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT041 精确日志管理组件</h1>
            <p><strong>功能分类：</strong>监控日志 (Monitoring Logging)</p>
            <p><strong>组件描述：</strong>精确日志操作、条件查询和痕迹清除</p>
            <p><strong>原子函数范围：</strong>URC_AT329-URC_AT336</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[监控日志<br/>Monitoring Logging]
    
    %% 组件
    URC_CT041[URC_CT041<br/>精确日志管理组件<br/>精确日志操作、条件查询和痕迹清除]
    
    %% 原子函数
    AT329[URC_AT329<br/>日志条目索引<br/>为日志文件创建索引]
    AT330[URC_AT330<br/>条件查询<br/>根据条件查询日志条目]
    AT331[URC_AT331<br/>精确删除<br/>删除指定的日志条目]
    AT332[URC_AT332<br/>日志重构<br/>重构日志文件结构]
    AT333[URC_AT333<br/>时间戳修改<br/>修改日志条目的时间戳]
    AT334[URC_AT334<br/>日志完整性维护<br/>维护日志文件的完整性]
    AT335[URC_AT335<br/>清理痕迹消除<br/>消除日志清理操作的痕迹]
    AT336[URC_AT336<br/>日志备份恢复<br/>备份和恢复日志文件]
    
    %% 关系连接
    CAT --> URC_CT041
    URC_CT041 --> AT329
    URC_CT041 --> AT330
    URC_CT041 --> AT331
    URC_CT041 --> AT332
    URC_CT041 --> AT333
    URC_CT041 --> AT334
    URC_CT041 --> AT335
    URC_CT041 --> AT336
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT041 componentStyle
    class AT329,AT330,AT331,AT332,AT333,AT334,AT335,AT336 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>