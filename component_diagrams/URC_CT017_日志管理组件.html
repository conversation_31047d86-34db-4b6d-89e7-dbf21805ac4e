<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT017 日志管理组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT017 日志管理组件</h1>
            <p><strong>功能分类：</strong>监控日志 (Monitoring Logging)</p>
            <p><strong>组件描述：</strong>日志记录、格式化和文件管理</p>
            <p><strong>原子函数范围：</strong>URC_AT113-URC_AT120</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[监控日志<br/>Monitoring Logging]
    
    %% 组件
    URC_CT017[URC_CT017<br/>日志管理组件<br/>日志记录、格式化和文件管理]
    
    %% 原子函数
    AT113[URC_AT113<br/>初始化日志系统<br/>初始化日志记录系统]
    AT114[URC_AT114<br/>写入日志<br/>写入日志条目]
    AT115[URC_AT115<br/>读取日志<br/>读取日志文件内容]
    AT116[URC_AT116<br/>日志轮转<br/>执行日志文件轮转]
    AT117[URC_AT117<br/>设置日志级别<br/>设置日志记录级别]
    AT118[URC_AT118<br/>格式化日志<br/>格式化日志输出]
    AT119[URC_AT119<br/>压缩日志<br/>压缩历史日志文件]
    AT120[URC_AT120<br/>清理日志<br/>清理过期日志文件]
    
    %% 关系连接
    CAT --> URC_CT017
    URC_CT017 --> AT113
    URC_CT017 --> AT114
    URC_CT017 --> AT115
    URC_CT017 --> AT116
    URC_CT017 --> AT117
    URC_CT017 --> AT118
    URC_CT017 --> AT119
    URC_CT017 --> AT120
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT017 componentStyle
    class AT113,AT114,AT115,AT116,AT117,AT118,AT119,AT120 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>