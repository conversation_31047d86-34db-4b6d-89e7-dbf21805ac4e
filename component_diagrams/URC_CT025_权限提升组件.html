<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT025 权限提升组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT025 权限提升组件</h1>
            <p><strong>功能分类：</strong>系统控制 (System Control)</p>
            <p><strong>组件描述：</strong>权限提升、漏洞利用和系统控制</p>
            <p><strong>原子函数范围：</strong>URC_AT201-URC_AT208</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[系统控制<br/>System Control]
    
    %% 组件
    URC_CT025[URC_CT025<br/>权限提升组件<br/>权限提升、漏洞利用和系统控制]
    
    %% 原子函数
    AT201[URC_AT201<br/>漏洞扫描<br/>扫描系统权限提升漏洞]
    AT202[URC_AT202<br/>UAC绕过<br/>绕过用户账户控制]
    AT203[URC_AT203<br/>令牌窃取<br/>窃取高权限访问令牌]
    AT204[URC_AT204<br/>进程注入<br/>注入到高权限进程]
    AT205[URC_AT205<br/>服务劫持<br/>劫持系统服务]
    AT206[URC_AT206<br/>DLL注入<br/>注入DLL到目标进程]
    AT207[URC_AT207<br/>内核利用<br/>利用内核漏洞提权]
    AT208[URC_AT208<br/>权限维持<br/>维持获得的高权限]
    
    %% 关系连接
    CAT --> URC_CT025
    URC_CT025 --> AT201
    URC_CT025 --> AT202
    URC_CT025 --> AT203
    URC_CT025 --> AT204
    URC_CT025 --> AT205
    URC_CT025 --> AT206
    URC_CT025 --> AT207
    URC_CT025 --> AT208
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT025 componentStyle
    class AT201,AT202,AT203,AT204,AT205,AT206,AT207,AT208 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>