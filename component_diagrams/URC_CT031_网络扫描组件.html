<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT031 网络扫描组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT031 网络扫描组件</h1>
            <p><strong>功能分类：</strong>横向移动 (Lateral Movement)</p>
            <p><strong>组件描述：</strong>网络发现、端口扫描和服务识别</p>
            <p><strong>原子函数范围：</strong>URC_AT249-URC_AT256</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[横向移动<br/>Lateral Movement]
    
    %% 组件
    URC_CT031[URC_CT031<br/>网络扫描组件<br/>网络发现、端口扫描和服务识别]
    
    %% 原子函数
    AT249[URC_AT249<br/>主机发现<br/>发现网络中的活跃主机]
    AT250[URC_AT250<br/>端口扫描<br/>扫描主机开放端口]
    AT251[URC_AT251<br/>服务识别<br/>识别端口运行的服务]
    AT252[URC_AT252<br/>操作系统指纹<br/>识别目标操作系统]
    AT253[URC_AT253<br/>漏洞扫描<br/>扫描已知安全漏洞]
    AT254[URC_AT254<br/>网络拓扑<br/>绘制网络拓扑结构]
    AT255[URC_AT255<br/>路由跟踪<br/>跟踪网络路由路径]
    AT256[URC_AT256<br/>扫描结果分析<br/>分析扫描结果]
    
    %% 关系连接
    CAT --> URC_CT031
    URC_CT031 --> AT249
    URC_CT031 --> AT250
    URC_CT031 --> AT251
    URC_CT031 --> AT252
    URC_CT031 --> AT253
    URC_CT031 --> AT254
    URC_CT031 --> AT255
    URC_CT031 --> AT256
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT031 componentStyle
    class AT249,AT250,AT251,AT252,AT253,AT254,AT255,AT256 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>