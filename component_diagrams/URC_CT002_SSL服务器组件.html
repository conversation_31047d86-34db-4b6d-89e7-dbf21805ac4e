<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT002 SSL服务器组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT002 SSL服务器组件</h1>
            <p><strong>功能分类：</strong>核心架构 (Core Architecture)</p>
            <p><strong>组件描述：</strong>SSL/TLS服务器初始化、证书管理和连接处理</p>
            <p><strong>原子函数范围：</strong>URC_AT009-URC_AT016</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[核心架构<br/>Core Architecture]
    
    %% 组件
    URC_CT002[URC_CT002<br/>SSL服务器组件<br/>SSL/TLS服务器初始化、证书管理和连接处理]
    
    %% 原子函数
    AT009[URC_AT009<br/>SSL上下文初始化<br/>初始化SSL上下文环境]
    AT010[URC_AT010<br/>证书加载<br/>加载SSL证书和私钥]
    AT011[URC_AT011<br/>服务器套接字创建<br/>创建服务器监听套接字]
    AT012[URC_AT012<br/>套接字绑定监听<br/>绑定套接字并开始监听]
    AT013[URC_AT013<br/>SSL连接接受<br/>接受新的SSL连接]
    AT014[URC_AT014<br/>SSL握手执行<br/>执行SSL握手过程]
    AT015[URC_AT015<br/>SSL选项配置<br/>配置SSL连接选项]
    AT016[URC_AT016<br/>SSL资源清理<br/>清理SSL相关资源]
    
    %% 关系连接
    CAT --> URC_CT002
    URC_CT002 --> AT009
    URC_CT002 --> AT010
    URC_CT002 --> AT011
    URC_CT002 --> AT012
    URC_CT002 --> AT013
    URC_CT002 --> AT014
    URC_CT002 --> AT015
    URC_CT002 --> AT016
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT002 componentStyle
    class AT009,AT010,AT011,AT012,AT013,AT014,AT015,AT016 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>