<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT005 TCP连接组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT005 TCP连接组件</h1>
            <p><strong>功能分类：</strong>网络通信 (Network Communication)</p>
            <p><strong>组件描述：</strong>TCP连接建立、数据传输和连接管理</p>
            <p><strong>原子函数范围：</strong>URC_AT057-URC_AT064</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[网络通信<br/>Network Communication]
    
    %% 组件
    URC_CT005[URC_CT005<br/>TCP连接组件<br/>TCP连接建立、数据传输和连接管理]
    
    %% 原子函数
    AT057[URC_AT057<br/>TCP套接字创建<br/>创建TCP套接字]
    AT058[URC_AT058<br/>TCP连接建立<br/>建立TCP连接]
    AT059[URC_AT059<br/>TCP数据发送<br/>通过TCP发送数据]
    AT060[URC_AT060<br/>TCP数据接收<br/>从TCP接收数据]
    AT061[URC_AT061<br/>TCP连接状态检查<br/>检查TCP连接状态]
    AT062[URC_AT062<br/>TCP连接重连<br/>重新建立TCP连接]
    AT063[URC_AT063<br/>TCP连接关闭<br/>关闭TCP连接]
    AT064[URC_AT064<br/>TCP错误处理<br/>处理TCP连接错误]
    
    %% 关系连接
    CAT --> URC_CT005
    URC_CT005 --> AT057
    URC_CT005 --> AT058
    URC_CT005 --> AT059
    URC_CT005 --> AT060
    URC_CT005 --> AT061
    URC_CT005 --> AT062
    URC_CT005 --> AT063
    URC_CT005 --> AT064
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT005 componentStyle
    class AT057,AT058,AT059,AT060,AT061,AT062,AT063,AT064 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>