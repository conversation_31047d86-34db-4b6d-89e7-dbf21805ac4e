<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT006 消息协议组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT006 消息协议组件</h1>
            <p><strong>功能分类：</strong>网络通信 (Network Communication)</p>
            <p><strong>组件描述：</strong>消息格式定义、编解码和协议处理</p>
            <p><strong>原子函数范围：</strong>URC_AT065-URC_AT072</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[网络通信<br/>Network Communication]
    
    %% 组件
    URC_CT006[URC_CT006<br/>消息协议组件<br/>消息格式定义、编解码和协议处理]
    
    %% 原子函数
    AT065[URC_AT065<br/>创建消息<br/>创建指定类型的消息]
    AT066[URC_AT066<br/>序列化消息<br/>将消息序列化为字节数组]
    AT067[URC_AT067<br/>反序列化消息<br/>从字节数组反序列化消息]
    AT068[URC_AT068<br/>验证消息<br/>验证消息格式和完整性]
    AT069[URC_AT069<br/>编码消息头<br/>编码消息头部信息]
    AT070[URC_AT070<br/>解码消息头<br/>解码消息头部信息]
    AT071[URC_AT071<br/>压缩消息<br/>压缩消息数据]
    AT072[URC_AT072<br/>解压缩消息<br/>解压缩消息数据]
    
    %% 关系连接
    CAT --> URC_CT006
    URC_CT006 --> AT065
    URC_CT006 --> AT066
    URC_CT006 --> AT067
    URC_CT006 --> AT068
    URC_CT006 --> AT069
    URC_CT006 --> AT070
    URC_CT006 --> AT071
    URC_CT006 --> AT072
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT006 componentStyle
    class AT065,AT066,AT067,AT068,AT069,AT070,AT071,AT072 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>