<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT028 键盘记录组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT028 键盘记录组件</h1>
            <p><strong>功能分类：</strong>数据收集 (Data Collection)</p>
            <p><strong>组件描述：</strong>键盘输入记录、按键捕获和数据收集</p>
            <p><strong>原子函数范围：</strong>URC_AT225-URC_AT232</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[数据收集<br/>Data Collection]
    
    %% 组件
    URC_CT028[URC_CT028<br/>键盘记录组件<br/>键盘输入记录、按键捕获和数据收集]
    
    %% 原子函数
    AT225[URC_AT225<br/>安装键盘钩子<br/>安装全局键盘钩子]
    AT226[URC_AT226<br/>捕获按键<br/>捕获键盘按键事件]
    AT227[URC_AT227<br/>记录按键<br/>记录按键到日志文件]
    AT228[URC_AT228<br/>窗口标题获取<br/>获取当前活动窗口标题]
    AT229[URC_AT229<br/>特殊键处理<br/>处理特殊功能键]
    AT230[URC_AT230<br/>按键过滤<br/>过滤敏感按键信息]
    AT231[URC_AT231<br/>日志加密<br/>加密键盘记录日志]
    AT232[URC_AT232<br/>定时上传<br/>定时上传键盘记录]
    
    %% 关系连接
    CAT --> URC_CT028
    URC_CT028 --> AT225
    URC_CT028 --> AT226
    URC_CT028 --> AT227
    URC_CT028 --> AT228
    URC_CT028 --> AT229
    URC_CT028 --> AT230
    URC_CT028 --> AT231
    URC_CT028 --> AT232
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT028 componentStyle
    class AT225,AT226,AT227,AT228,AT229,AT230,AT231,AT232 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>