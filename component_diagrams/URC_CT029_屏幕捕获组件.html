<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT029 屏幕捕获组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT029 屏幕捕获组件</h1>
            <p><strong>功能分类：</strong>数据收集 (Data Collection)</p>
            <p><strong>组件描述：</strong>屏幕截图、视频录制和图像处理</p>
            <p><strong>原子函数范围：</strong>URC_AT233-URC_AT240</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[数据收集<br/>Data Collection]
    
    %% 组件
    URC_CT029[URC_CT029<br/>屏幕捕获组件<br/>屏幕截图、视频录制和图像处理]
    
    %% 原子函数
    AT233[URC_AT233<br/>屏幕截图<br/>捕获屏幕截图]
    AT234[URC_AT234<br/>窗口截图<br/>捕获指定窗口截图]
    AT235[URC_AT235<br/>视频录制<br/>录制屏幕视频]
    AT236[URC_AT236<br/>图像压缩<br/>压缩截图图像]
    AT237[URC_AT237<br/>定时截图<br/>定时自动截图]
    AT238[URC_AT238<br/>鼠标轨迹<br/>记录鼠标移动轨迹]
    AT239[URC_AT239<br/>屏幕变化检测<br/>检测屏幕内容变化]
    AT240[URC_AT240<br/>多显示器支持<br/>支持多显示器截图]
    
    %% 关系连接
    CAT --> URC_CT029
    URC_CT029 --> AT233
    URC_CT029 --> AT234
    URC_CT029 --> AT235
    URC_CT029 --> AT236
    URC_CT029 --> AT237
    URC_CT029 --> AT238
    URC_CT029 --> AT239
    URC_CT029 --> AT240
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT029 componentStyle
    class AT233,AT234,AT235,AT236,AT237,AT238,AT239,AT240 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>