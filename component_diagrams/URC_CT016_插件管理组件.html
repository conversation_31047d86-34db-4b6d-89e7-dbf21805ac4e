<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT016 插件管理组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT016 插件管理组件</h1>
            <p><strong>功能分类：</strong>动态加载 (Dynamic Loading)</p>
            <p><strong>组件描述：</strong>插件加载、接口管理和依赖处理</p>
            <p><strong>原子函数范围：</strong>URC_AT145-URC_AT152</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[动态加载<br/>Dynamic Loading]
    
    %% 组件
    URC_CT016[URC_CT016<br/>插件管理组件<br/>插件加载、接口管理和依赖处理]
    
    %% 原子函数
    AT145[URC_AT145<br/>加载插件<br/>加载插件文件]
    AT146[URC_AT146<br/>卸载插件<br/>卸载已加载的插件]
    AT147[URC_AT147<br/>获取插件信息<br/>获取插件详细信息]
    AT148[URC_AT148<br/>调用插件函数<br/>调用插件中的函数]
    AT149[URC_AT149<br/>列出可用插件<br/>列出所有可用插件]
    AT150[URC_AT150<br/>验证插件签名<br/>验证插件数字签名]
    AT151[URC_AT151<br/>注册插件接口<br/>注册插件接口]
    AT152[URC_AT152<br/>获取插件依赖<br/>获取插件依赖关系]
    
    %% 关系连接
    CAT --> URC_CT016
    URC_CT016 --> AT145
    URC_CT016 --> AT146
    URC_CT016 --> AT147
    URC_CT016 --> AT148
    URC_CT016 --> AT149
    URC_CT016 --> AT150
    URC_CT016 --> AT151
    URC_CT016 --> AT152
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT016 componentStyle
    class AT145,AT146,AT147,AT148,AT149,AT150,AT151,AT152 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>