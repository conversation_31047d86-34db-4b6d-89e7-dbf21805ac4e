<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT004 TLS加密通道组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT004 TLS加密通道组件</h1>
            <p><strong>功能分类：</strong>网络通信 (Network Communication)</p>
            <p><strong>组件描述：</strong>TLS加密通信、数据加密解密和安全传输</p>
            <p><strong>原子函数范围：</strong>URC_AT049-URC_AT056</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[网络通信<br/>Network Communication]
    
    %% 组件
    URC_CT004[URC_CT004<br/>TLS加密通道组件<br/>TLS加密通信、数据加密解密和安全传输]
    
    %% 原子函数
    AT049[URC_AT049<br/>TLS上下文初始化<br/>初始化TLS通信上下文]
    AT050[URC_AT050<br/>创建TLS连接<br/>创建TLS加密连接]
    AT051[URC_AT051<br/>数据加密<br/>使用指定密钥加密数据]
    AT052[URC_AT052<br/>数据解密<br/>使用指定密钥解密数据]
    AT053[URC_AT053<br/>发送安全消息<br/>通过TLS发送加密消息]
    AT054[URC_AT054<br/>接收安全消息<br/>通过TLS接收加密消息]
    AT055[URC_AT055<br/>验证证书<br/>验证TLS证书有效性]
    AT056[URC_AT056<br/>关闭TLS连接<br/>安全关闭TLS连接]
    
    %% 关系连接
    CAT --> URC_CT004
    URC_CT004 --> AT049
    URC_CT004 --> AT050
    URC_CT004 --> AT051
    URC_CT004 --> AT052
    URC_CT004 --> AT053
    URC_CT004 --> AT054
    URC_CT004 --> AT055
    URC_CT004 --> AT056
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT004 componentStyle
    class AT049,AT050,AT051,AT052,AT053,AT054,AT055,AT056 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>