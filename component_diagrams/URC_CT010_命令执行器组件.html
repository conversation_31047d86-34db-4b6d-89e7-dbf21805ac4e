<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT010 命令执行器组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT010 命令执行器组件</h1>
            <p><strong>功能分类：</strong>命令执行 (Command Execution)</p>
            <p><strong>组件描述：</strong>系统命令执行、进程管理和输出捕获</p>
            <p><strong>原子函数范围：</strong>URC_AT081-URC_AT088</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[命令执行<br/>Command Execution]
    
    %% 组件
    URC_CT010[URC_CT010<br/>命令执行器组件<br/>系统命令执行、进程管理和输出捕获]
    
    %% 原子函数
    AT081[URC_AT081<br/>执行Shell命令<br/>执行系统Shell命令]
    AT082[URC_AT082<br/>创建子进程<br/>创建子进程执行命令]
    AT083[URC_AT083<br/>监控进程执行<br/>监控子进程执行状态]
    AT084[URC_AT084<br/>捕获进程输出<br/>捕获子进程的输出]
    AT085[URC_AT085<br/>终止进程<br/>强制终止指定进程]
    AT086[URC_AT086<br/>设置进程超时<br/>设置进程执行超时]
    AT087[URC_AT087<br/>获取进程退出码<br/>获取进程退出状态码]
    AT088[URC_AT088<br/>清理进程资源<br/>清理进程相关资源]
    
    %% 关系连接
    CAT --> URC_CT010
    URC_CT010 --> AT081
    URC_CT010 --> AT082
    URC_CT010 --> AT083
    URC_CT010 --> AT084
    URC_CT010 --> AT085
    URC_CT010 --> AT086
    URC_CT010 --> AT087
    URC_CT010 --> AT088
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT010 componentStyle
    class AT081,AT082,AT083,AT084,AT085,AT086,AT087,AT088 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>