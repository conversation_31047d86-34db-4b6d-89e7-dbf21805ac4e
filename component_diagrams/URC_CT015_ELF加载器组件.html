<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT015 ELF加载器组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT015 ELF加载器组件</h1>
            <p><strong>功能分类：</strong>动态加载 (Dynamic Loading)</p>
            <p><strong>组件描述：</strong>动态库加载、符号解析和内存管理</p>
            <p><strong>原子函数范围：</strong>URC_AT105-URC_AT112</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[动态加载<br/>Dynamic Loading]
    
    %% 组件
    URC_CT015[URC_CT015<br/>ELF加载器组件<br/>动态库加载、符号解析和内存管理]
    
    %% 原子函数
    AT105[URC_AT105<br/>加载ELF文件<br/>动态加载ELF文件]
    AT106[URC_AT106<br/>验证ELF头<br/>验证ELF文件头部]
    AT107[URC_AT107<br/>解析符号<br/>解析ELF符号表]
    AT108[URC_AT108<br/>执行ELF函数<br/>执行ELF文件中的函数]
    AT109[URC_AT109<br/>卸载ELF文件<br/>卸载已加载的ELF文件]
    AT110[URC_AT110<br/>获取ELF信息<br/>获取ELF文件信息]
    AT111[URC_AT111<br/>映射ELF段<br/>映射ELF文件段到内存]
    AT112[URC_AT112<br/>重定位ELF符号<br/>重定位ELF符号地址]
    
    %% 关系连接
    CAT --> URC_CT015
    URC_CT015 --> AT105
    URC_CT015 --> AT106
    URC_CT015 --> AT107
    URC_CT015 --> AT108
    URC_CT015 --> AT109
    URC_CT015 --> AT110
    URC_CT015 --> AT111
    URC_CT015 --> AT112
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT015 componentStyle
    class AT105,AT106,AT107,AT108,AT109,AT110,AT111,AT112 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>