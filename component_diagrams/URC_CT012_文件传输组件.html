<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT012 文件传输组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT012 文件传输组件</h1>
            <p><strong>功能分类：</strong>文件操作 (File Operations)</p>
            <p><strong>组件描述：</strong>文件上传下载、断点续传和完整性验证</p>
            <p><strong>原子函数范围：</strong>URC_AT129-URC_AT136</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[文件操作<br/>File Operations]
    
    %% 组件
    URC_CT012[URC_CT012<br/>文件传输组件<br/>文件上传下载、断点续传和完整性验证]
    
    %% 原子函数
    AT129[URC_AT129<br/>初始化传输<br/>初始化文件传输会话]
    AT130[URC_AT130<br/>发送文件块<br/>发送文件数据块]
    AT131[URC_AT131<br/>接收文件块<br/>接收文件数据块]
    AT132[URC_AT132<br/>恢复传输<br/>恢复中断的文件传输]
    AT133[URC_AT133<br/>验证文件完整性<br/>验证文件传输完整性]
    AT134[URC_AT134<br/>压缩文件<br/>压缩文件数据]
    AT135[URC_AT135<br/>解压缩文件<br/>解压缩文件数据]
    AT136[URC_AT136<br/>计算传输进度<br/>计算文件传输进度]
    
    %% 关系连接
    CAT --> URC_CT012
    URC_CT012 --> AT129
    URC_CT012 --> AT130
    URC_CT012 --> AT131
    URC_CT012 --> AT132
    URC_CT012 --> AT133
    URC_CT012 --> AT134
    URC_CT012 --> AT135
    URC_CT012 --> AT136
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT012 componentStyle
    class AT129,AT130,AT131,AT132,AT133,AT134,AT135,AT136 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>