<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT001 主程序入口组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT001 主程序入口组件</h1>
            <p><strong>功能分类：</strong>核心架构 (Core Architecture)</p>
            <p><strong>组件描述：</strong>程序启动、初始化、主循环和资源管理</p>
            <p><strong>原子函数范围：</strong>URC_AT001-URC_AT008</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[核心架构<br/>Core Architecture]
    
    %% 组件
    URC_CT001[URC_CT001<br/>主程序入口组件<br/>程序启动、初始化、主循环和资源管理]
    
    %% 原子函数
    AT001[URC_AT001<br/>程序初始化<br/>初始化程序运行环境和全局变量]
    AT002[URC_AT002<br/>命令行参数解析<br/>解析和验证命令行参数]
    AT003[URC_AT003<br/>信号处理器设置<br/>设置程序信号处理函数]
    AT004[URC_AT004<br/>主循环启动<br/>启动程序主事件循环]
    AT005[URC_AT005<br/>资源清理<br/>清理程序使用的所有资源]
    AT006[URC_AT006<br/>关闭处理<br/>处理程序关闭流程]
    AT007[URC_AT007<br/>配置验证<br/>验证程序配置参数]
    AT008[URC_AT008<br/>程序退出<br/>安全退出程序]
    
    %% 关系连接
    CAT --> URC_CT001
    URC_CT001 --> AT001
    URC_CT001 --> AT002
    URC_CT001 --> AT003
    URC_CT001 --> AT004
    URC_CT001 --> AT005
    URC_CT001 --> AT006
    URC_CT001 --> AT007
    URC_CT001 --> AT008
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT001 componentStyle
    class AT001,AT002,AT003,AT004,AT005,AT006,AT007,AT008 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>