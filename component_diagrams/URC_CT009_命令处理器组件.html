<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT009 命令处理器组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT009 命令处理器组件</h1>
            <p><strong>功能分类：</strong>命令执行 (Command Execution)</p>
            <p><strong>组件描述：</strong>命令解析、验证和分发处理</p>
            <p><strong>原子函数范围：</strong>URC_AT033-URC_AT040</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[命令执行<br/>Command Execution]
    
    %% 组件
    URC_CT009[URC_CT009<br/>命令处理器组件<br/>命令解析、验证和分发处理]
    
    %% 原子函数
    AT033[URC_AT033<br/>命令解析<br/>解析接收到的命令字符串]
    AT034[URC_AT034<br/>命令验证<br/>验证命令的合法性和权限]
    AT035[URC_AT035<br/>命令分发<br/>将命令分发给相应的处理器]
    AT036[URC_AT036<br/>命令执行<br/>执行具体的命令操作]
    AT037[URC_AT037<br/>响应格式化<br/>格式化命令执行结果]
    AT038[URC_AT038<br/>发送响应<br/>向客户端发送命令响应]
    AT039[URC_AT039<br/>记录命令执行<br/>记录命令执行日志]
    AT040[URC_AT040<br/>处理命令错误<br/>处理命令执行错误]
    
    %% 关系连接
    CAT --> URC_CT009
    URC_CT009 --> AT033
    URC_CT009 --> AT034
    URC_CT009 --> AT035
    URC_CT009 --> AT036
    URC_CT009 --> AT037
    URC_CT009 --> AT038
    URC_CT009 --> AT039
    URC_CT009 --> AT040
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT009 componentStyle
    class AT033,AT034,AT035,AT036,AT037,AT038,AT039,AT040 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>