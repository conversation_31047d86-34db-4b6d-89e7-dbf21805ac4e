<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT019 SOCKS代理组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT019 SOCKS代理组件</h1>
            <p><strong>功能分类：</strong>网络代理 (Network Proxy)</p>
            <p><strong>组件描述：</strong>SOCKS代理服务、连接转发和协议处理</p>
            <p><strong>原子函数范围：</strong>URC_AT153-URC_AT160</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[网络代理<br/>Network Proxy]
    
    %% 组件
    URC_CT019[URC_CT019<br/>SOCKS代理组件<br/>SOCKS代理服务、连接转发和协议处理]
    
    %% 原子函数
    AT153[URC_AT153<br/>创建SOCKS服务器<br/>创建SOCKS代理服务器]
    AT154[URC_AT154<br/>处理SOCKS连接<br/>处理SOCKS客户端连接]
    AT155[URC_AT155<br/>SOCKS握手<br/>执行SOCKS协议握手]
    AT156[URC_AT156<br/>转发流量<br/>转发客户端和目标服务器之间的流量]
    AT157[URC_AT157<br/>关闭SOCKS连接<br/>关闭SOCKS代理连接]
    AT158[URC_AT158<br/>验证SOCKS请求<br/>验证SOCKS请求的有效性]
    AT159[URC_AT159<br/>创建目标连接<br/>创建到目标服务器的连接]
    AT160[URC_AT160<br/>记录SOCKS活动<br/>记录SOCKS代理活动日志]
    
    %% 关系连接
    CAT --> URC_CT019
    URC_CT019 --> AT153
    URC_CT019 --> AT154
    URC_CT019 --> AT155
    URC_CT019 --> AT156
    URC_CT019 --> AT157
    URC_CT019 --> AT158
    URC_CT019 --> AT159
    URC_CT019 --> AT160
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT019 componentStyle
    class AT153,AT154,AT155,AT156,AT157,AT158,AT159,AT160 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>