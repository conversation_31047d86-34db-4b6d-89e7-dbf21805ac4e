<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT021 HTTP通信模拟组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT021 HTTP通信模拟组件</h1>
            <p><strong>功能分类：</strong>HTTP通信 (HTTP Communication)</p>
            <p><strong>组件描述：</strong>HTTP协议模拟、请求处理和响应生成</p>
            <p><strong>原子函数范围：</strong>URC_AT169-URC_AT176</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[HTTP通信<br/>HTTP Communication]
    
    %% 组件
    URC_CT021[URC_CT021<br/>HTTP通信模拟组件<br/>HTTP协议模拟、请求处理和响应生成]
    
    %% 原子函数
    AT169[URC_AT169<br/>发送HTTP请求<br/>发送HTTP请求到服务器]
    AT170[URC_AT170<br/>接收HTTP响应<br/>接收HTTP服务器响应]
    AT171[URC_AT171<br/>解析HTTP头<br/>解析HTTP响应头]
    AT172[URC_AT172<br/>生成HTTP响应<br/>生成HTTP响应消息]
    AT173[URC_AT173<br/>编码HTTP数据<br/>编码HTTP数据]
    AT174[URC_AT174<br/>解码HTTP数据<br/>解码HTTP数据]
    AT175[URC_AT175<br/>验证HTTP请求<br/>验证HTTP请求有效性]
    AT176[URC_AT176<br/>处理HTTP错误<br/>处理HTTP错误响应]
    
    %% 关系连接
    CAT --> URC_CT021
    URC_CT021 --> AT169
    URC_CT021 --> AT170
    URC_CT021 --> AT171
    URC_CT021 --> AT172
    URC_CT021 --> AT173
    URC_CT021 --> AT174
    URC_CT021 --> AT175
    URC_CT021 --> AT176
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT021 componentStyle
    class AT169,AT170,AT171,AT172,AT173,AT174,AT175,AT176 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>