<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT011 文件处理器组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT011 文件处理器组件</h1>
            <p><strong>功能分类：</strong>文件操作 (File Operations)</p>
            <p><strong>组件描述：</strong>文件操作、目录管理和权限控制</p>
            <p><strong>原子函数范围：</strong>URC_AT089-URC_AT096</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[文件操作<br/>File Operations]
    
    %% 组件
    URC_CT011[URC_CT011<br/>文件处理器组件<br/>文件操作、目录管理和权限控制]
    
    %% 原子函数
    AT089[URC_AT089<br/>上传文件<br/>将本地文件上传到远程]
    AT090[URC_AT090<br/>下载文件<br/>从远程下载文件到本地]
    AT091[URC_AT091<br/>删除文件<br/>删除指定文件]
    AT092[URC_AT092<br/>创建目录<br/>创建目录结构]
    AT093[URC_AT093<br/>列出目录<br/>列出目录内容]
    AT094[URC_AT094<br/>获取文件信息<br/>获取文件详细信息]
    AT095[URC_AT095<br/>复制文件<br/>复制文件到新位置]
    AT096[URC_AT096<br/>移动文件<br/>移动文件到新位置]
    
    %% 关系连接
    CAT --> URC_CT011
    URC_CT011 --> AT089
    URC_CT011 --> AT090
    URC_CT011 --> AT091
    URC_CT011 --> AT092
    URC_CT011 --> AT093
    URC_CT011 --> AT094
    URC_CT011 --> AT095
    URC_CT011 --> AT096
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT011 componentStyle
    class AT089,AT090,AT091,AT092,AT093,AT094,AT095,AT096 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>