<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT023 持久化机制组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT023 持久化机制组件</h1>
            <p><strong>功能分类：</strong>高级安全 (Advanced Security)</p>
            <p><strong>组件描述：</strong>系统持久化、自启动和隐蔽驻留</p>
            <p><strong>原子函数范围：</strong>URC_AT185-URC_AT192</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[高级安全<br/>Advanced Security]
    
    %% 组件
    URC_CT023[URC_CT023<br/>持久化机制组件<br/>系统持久化、自启动和隐蔽驻留]
    
    %% 原子函数
    AT185[URC_AT185<br/>创建启动项<br/>创建系统启动项]
    AT186[URC_AT186<br/>注册服务<br/>注册为系统服务]
    AT187[URC_AT187<br/>定时任务<br/>创建定时执行任务]
    AT188[URC_AT188<br/>文件关联<br/>创建文件类型关联]
    AT189[URC_AT189<br/>DLL劫持<br/>实施DLL劫持技术]
    AT190[URC_AT190<br/>注册表持久化<br/>通过注册表实现持久化]
    AT191[URC_AT191<br/>WMI持久化<br/>通过WMI事件实现持久化]
    AT192[URC_AT192<br/>隐藏文件<br/>隐藏持久化相关文件]
    
    %% 关系连接
    CAT --> URC_CT023
    URC_CT023 --> AT185
    URC_CT023 --> AT186
    URC_CT023 --> AT187
    URC_CT023 --> AT188
    URC_CT023 --> AT189
    URC_CT023 --> AT190
    URC_CT023 --> AT191
    URC_CT023 --> AT192
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT023 componentStyle
    class AT185,AT186,AT187,AT188,AT189,AT190,AT191,AT192 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>