<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件关系图索引</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #4a148c; padding-bottom: 20px; }
        .header h1 { color: #4a148c; margin: 0; }
        .stats { display: flex; justify-content: center; gap: 20px; margin: 20px 0; }
        .stat { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 10px 20px; border-radius: 8px; text-align: center; }
        .category { margin: 20px 0; border: 1px solid #ddd; border-radius: 6px; }
        .category-header { background: #4a148c; color: white; padding: 10px 15px; font-weight: bold; }
        .components { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 10px; padding: 15px; }
        .component { border: 1px solid #ddd; padding: 10px; border-radius: 4px; background: #fafafa; cursor: pointer; transition: all 0.3s; }
        .component:hover { background: #f0f0f0; border-color: #4a148c; transform: translateY(-1px); }
        .component-id { font-weight: bold; color: #4a148c; }
        .component-name { color: #333; margin: 5px 0; }
        .atomic-range { background: #e8f5e8; color: #1b5e20; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Ubuntu远程控制工具 - 组件关系图索引</h1>
            <div class="stats">
                <div class="stat"><strong>42</strong><br>组件</div>
                <div class="stat"><strong>344</strong><br>原子函数</div>
                <div class="stat"><strong>15</strong><br>功能分类</div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">核心架构 (3个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT001_主程序入口组件.html', '_blank')">
                    <div class="component-id">URC_CT001</div>
                    <div class="component-name">主程序入口组件</div>
                    <div class="atomic-range">URC_AT001-008</div>
                </div>
                <div class="component" onclick="window.open('URC_CT002_SSL服务器组件.html', '_blank')">
                    <div class="component-id">URC_CT002</div>
                    <div class="component-name">SSL服务器组件</div>
                    <div class="atomic-range">URC_AT009-016</div>
                </div>
                <div class="component" onclick="window.open('URC_CT003_客户端管理器组件.html', '_blank')">
                    <div class="component-id">URC_CT003</div>
                    <div class="component-name">客户端管理器组件</div>
                    <div class="atomic-range">URC_AT017-024</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">网络通信 (4个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT004_TLS加密通道组件.html', '_blank')">
                    <div class="component-id">URC_CT004</div>
                    <div class="component-name">TLS加密通道组件</div>
                    <div class="atomic-range">URC_AT049-056</div>
                </div>
                <div class="component" onclick="window.open('URC_CT005_TCP连接组件.html', '_blank')">
                    <div class="component-id">URC_CT005</div>
                    <div class="component-name">TCP连接组件</div>
                    <div class="atomic-range">URC_AT057-064</div>
                </div>
                <div class="component" onclick="window.open('URC_CT006_消息协议组件.html', '_blank')">
                    <div class="component-id">URC_CT006</div>
                    <div class="component-name">消息协议组件</div>
                    <div class="atomic-range">URC_AT065-072</div>
                </div>
                <div class="component" onclick="window.open('URC_CT037_端口复用组件.html', '_blank')">
                    <div class="component-id">URC_CT037</div>
                    <div class="component-name">端口复用组件</div>
                    <div class="atomic-range">URC_AT297-304</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">认证安全 (2个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT007_认证管理器组件.html', '_blank')">
                    <div class="component-id">URC_CT007</div>
                    <div class="component-name">认证管理器组件</div>
                    <div class="atomic-range">URC_AT041-048</div>
                </div>
                <div class="component" onclick="window.open('URC_CT008_加密解密组件.html', '_blank')">
                    <div class="component-id">URC_CT008</div>
                    <div class="component-name">加密解密组件</div>
                    <div class="atomic-range">URC_AT121-128</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">命令执行 (3个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT009_命令处理器组件.html', '_blank')">
                    <div class="component-id">URC_CT009</div>
                    <div class="component-name">命令处理器组件</div>
                    <div class="atomic-range">URC_AT033-040</div>
                </div>
                <div class="component" onclick="window.open('URC_CT010_命令执行器组件.html', '_blank')">
                    <div class="component-id">URC_CT010</div>
                    <div class="component-name">命令执行器组件</div>
                    <div class="atomic-range">URC_AT081-088</div>
                </div>
                <div class="component" onclick="window.open('URC_CT038_交互式终端组件.html', '_blank')">
                    <div class="component-id">URC_CT038</div>
                    <div class="component-name">交互式终端组件</div>
                    <div class="atomic-range">URC_AT305-312</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">文件操作 (2个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT011_文件处理器组件.html', '_blank')">
                    <div class="component-id">URC_CT011</div>
                    <div class="component-name">文件处理器组件</div>
                    <div class="atomic-range">URC_AT089-096</div>
                </div>
                <div class="component" onclick="window.open('URC_CT012_文件传输组件.html', '_blank')">
                    <div class="component-id">URC_CT012</div>
                    <div class="component-name">文件传输组件</div>
                    <div class="atomic-range">URC_AT129-136</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">系统监控 (2个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT013_系统信息收集器组件.html', '_blank')">
                    <div class="component-id">URC_CT013</div>
                    <div class="component-name">系统信息收集器组件</div>
                    <div class="atomic-range">URC_AT097-104</div>
                </div>
                <div class="component" onclick="window.open('URC_CT014_进程管理组件.html', '_blank')">
                    <div class="component-id">URC_CT014</div>
                    <div class="component-name">进程管理组件</div>
                    <div class="atomic-range">URC_AT137-144</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">动态加载 (2个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT015_ELF加载器组件.html', '_blank')">
                    <div class="component-id">URC_CT015</div>
                    <div class="component-name">ELF加载器组件</div>
                    <div class="atomic-range">URC_AT105-112</div>
                </div>
                <div class="component" onclick="window.open('URC_CT016_插件管理组件.html', '_blank')">
                    <div class="component-id">URC_CT016</div>
                    <div class="component-name">插件管理组件</div>
                    <div class="atomic-range">URC_AT145-152</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">监控日志 (3个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT017_日志管理组件.html', '_blank')">
                    <div class="component-id">URC_CT017</div>
                    <div class="component-name">日志管理组件</div>
                    <div class="atomic-range">URC_AT113-120</div>
                </div>
                <div class="component" onclick="window.open('URC_CT018_心跳监控组件.html', '_blank')">
                    <div class="component-id">URC_CT018</div>
                    <div class="component-name">心跳监控组件</div>
                    <div class="atomic-range">URC_AT025-032</div>
                </div>
                <div class="component" onclick="window.open('URC_CT041_精确日志管理组件.html', '_blank')">
                    <div class="component-id">URC_CT041</div>
                    <div class="component-name">精确日志管理组件</div>
                    <div class="atomic-range">URC_AT329-336</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">网络代理 (2个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT019_SOCKS代理组件.html', '_blank')">
                    <div class="component-id">URC_CT019</div>
                    <div class="component-name">SOCKS代理组件</div>
                    <div class="atomic-range">URC_AT153-160</div>
                </div>
                <div class="component" onclick="window.open('URC_CT020_端口转发组件.html', '_blank')">
                    <div class="component-id">URC_CT020</div>
                    <div class="component-name">端口转发组件</div>
                    <div class="atomic-range">URC_AT161-168</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">HTTP通信 (2个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT021_HTTP通信模拟组件.html', '_blank')">
                    <div class="component-id">URC_CT021</div>
                    <div class="component-name">HTTP通信模拟组件</div>
                    <div class="atomic-range">URC_AT169-176</div>
                </div>
                <div class="component" onclick="window.open('URC_CT039_Web管理界面组件.html', '_blank')">
                    <div class="component-id">URC_CT039</div>
                    <div class="component-name">Web管理界面组件</div>
                    <div class="atomic-range">URC_AT313-320</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">高级安全 (4个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT022_反调试检测组件.html', '_blank')">
                    <div class="component-id">URC_CT022</div>
                    <div class="component-name">反调试检测组件</div>
                    <div class="atomic-range">URC_AT177-184</div>
                </div>
                <div class="component" onclick="window.open('URC_CT023_持久化机制组件.html', '_blank')">
                    <div class="component-id">URC_CT023</div>
                    <div class="component-name">持久化机制组件</div>
                    <div class="atomic-range">URC_AT185-192</div>
                </div>
                <div class="component" onclick="window.open('URC_CT024_网络隐蔽组件.html', '_blank')">
                    <div class="component-id">URC_CT024</div>
                    <div class="component-name">网络隐蔽组件</div>
                    <div class="atomic-range">URC_AT193-200</div>
                </div>
                <div class="component" onclick="window.open('URC_CT042_持久化增强组件.html', '_blank')">
                    <div class="component-id">URC_CT042</div>
                    <div class="component-name">持久化增强组件</div>
                    <div class="atomic-range">URC_AT337-344</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">系统控制 (4个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT025_权限提升组件.html', '_blank')">
                    <div class="component-id">URC_CT025</div>
                    <div class="component-name">权限提升组件</div>
                    <div class="atomic-range">URC_AT201-208</div>
                </div>
                <div class="component" onclick="window.open('URC_CT026_系统监控组件.html', '_blank')">
                    <div class="component-id">URC_CT026</div>
                    <div class="component-name">系统监控组件</div>
                    <div class="atomic-range">URC_AT209-216</div>
                </div>
                <div class="component" onclick="window.open('URC_CT027_注册表操作组件.html', '_blank')">
                    <div class="component-id">URC_CT027</div>
                    <div class="component-name">注册表操作组件</div>
                    <div class="atomic-range">URC_AT217-224</div>
                </div>
                <div class="component" onclick="window.open('URC_CT040_热升级组件.html', '_blank')">
                    <div class="component-id">URC_CT040</div>
                    <div class="component-name">热升级组件</div>
                    <div class="atomic-range">URC_AT321-328</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">数据收集 (3个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT028_键盘记录组件.html', '_blank')">
                    <div class="component-id">URC_CT028</div>
                    <div class="component-name">键盘记录组件</div>
                    <div class="atomic-range">URC_AT225-232</div>
                </div>
                <div class="component" onclick="window.open('URC_CT029_屏幕捕获组件.html', '_blank')">
                    <div class="component-id">URC_CT029</div>
                    <div class="component-name">屏幕捕获组件</div>
                    <div class="atomic-range">URC_AT233-240</div>
                </div>
                <div class="component" onclick="window.open('URC_CT030_网络嗅探组件.html', '_blank')">
                    <div class="component-id">URC_CT030</div>
                    <div class="component-name">网络嗅探组件</div>
                    <div class="atomic-range">URC_AT241-248</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">横向移动 (3个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT031_网络扫描组件.html', '_blank')">
                    <div class="component-id">URC_CT031</div>
                    <div class="component-name">网络扫描组件</div>
                    <div class="atomic-range">URC_AT249-256</div>
                </div>
                <div class="component" onclick="window.open('URC_CT032_凭据窃取组件.html', '_blank')">
                    <div class="component-id">URC_CT032</div>
                    <div class="component-name">凭据窃取组件</div>
                    <div class="atomic-range">URC_AT257-264</div>
                </div>
                <div class="component" onclick="window.open('URC_CT033_远程执行组件.html', '_blank')">
                    <div class="component-id">URC_CT033</div>
                    <div class="component-name">远程执行组件</div>
                    <div class="atomic-range">URC_AT265-272</div>
                </div>
            </div>
        </div>

        <div class="category">
            <div class="category-header">数据外泄 (3个组件)</div>
            <div class="components">
                <div class="component" onclick="window.open('URC_CT034_数据搜索组件.html', '_blank')">
                    <div class="component-id">URC_CT034</div>
                    <div class="component-name">数据搜索组件</div>
                    <div class="atomic-range">URC_AT273-280</div>
                </div>
                <div class="component" onclick="window.open('URC_CT035_数据压缩组件.html', '_blank')">
                    <div class="component-id">URC_CT035</div>
                    <div class="component-name">数据压缩组件</div>
                    <div class="atomic-range">URC_AT281-288</div>
                </div>
                <div class="component" onclick="window.open('URC_CT036_隐蔽传输组件.html', '_blank')">
                    <div class="component-id">URC_CT036</div>
                    <div class="component-name">隐蔽传输组件</div>
                    <div class="atomic-range">URC_AT289-296</div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
            <p>点击任意组件查看详细的组件与原子函数关系图</p>
            <p>共42个组件，344个原子函数，15个功能分类</p>
        </div>
    </div>
</body>
</html>
