<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT014 进程管理组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT014 进程管理组件</h1>
            <p><strong>功能分类：</strong>系统监控 (System Monitoring)</p>
            <p><strong>组件描述：</strong>进程监控、控制和资源管理</p>
            <p><strong>原子函数范围：</strong>URC_AT137-URC_AT144</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[系统监控<br/>System Monitoring]
    
    %% 组件
    URC_CT014[URC_CT014<br/>进程管理组件<br/>进程监控、控制和资源管理]
    
    %% 原子函数
    AT137[URC_AT137<br/>列出运行进程<br/>列出当前运行的进程]
    AT138[URC_AT138<br/>获取进程信息<br/>获取指定进程的详细信息]
    AT139[URC_AT139<br/>杀死进程<br/>终止指定进程]
    AT140[URC_AT140<br/>暂停进程<br/>暂停进程执行]
    AT141[URC_AT141<br/>恢复进程<br/>恢复进程执行]
    AT142[URC_AT142<br/>设置进程优先级<br/>设置进程优先级]
    AT143[URC_AT143<br/>监控进程资源<br/>监控进程资源使用]
    AT144[URC_AT144<br/>获取进程树<br/>获取进程树结构]
    
    %% 关系连接
    CAT --> URC_CT014
    URC_CT014 --> AT137
    URC_CT014 --> AT138
    URC_CT014 --> AT139
    URC_CT014 --> AT140
    URC_CT014 --> AT141
    URC_CT014 --> AT142
    URC_CT014 --> AT143
    URC_CT014 --> AT144
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT014 componentStyle
    class AT137,AT138,AT139,AT140,AT141,AT142,AT143,AT144 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>