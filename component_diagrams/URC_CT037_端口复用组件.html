<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT037 端口复用组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT037 端口复用组件</h1>
            <p><strong>功能分类：</strong>网络通信 (Network Communication)</p>
            <p><strong>组件描述：</strong>单端口多协议复用和流量分发</p>
            <p><strong>原子函数范围：</strong>URC_AT297-URC_AT304</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[网络通信<br/>Network Communication]
    
    %% 组件
    URC_CT037[URC_CT037<br/>端口复用组件<br/>单端口多协议复用和流量分发]
    
    %% 原子函数
    AT297[URC_AT297<br/>协议识别<br/>通过数据包特征识别协议类型]
    AT298[URC_AT298<br/>流量分发<br/>根据协议类型分发连接到相应处理器]
    AT299[URC_AT299<br/>HTTP协议处理<br/>处理HTTP协议连接和请求]
    AT300[URC_AT300<br/>SSH协议处理<br/>处理SSH协议连接和认证]
    AT301[URC_AT301<br/>自定义协议处理<br/>处理自定义协议连接]
    AT302[URC_AT302<br/>连接状态管理<br/>管理多协议连接的状态信息]
    AT303[URC_AT303<br/>协议切换<br/>在连接过程中切换协议类型]
    AT304[URC_AT304<br/>端口监听管理<br/>管理多协议复用的端口监听]
    
    %% 关系连接
    CAT --> URC_CT037
    URC_CT037 --> AT297
    URC_CT037 --> AT298
    URC_CT037 --> AT299
    URC_CT037 --> AT300
    URC_CT037 --> AT301
    URC_CT037 --> AT302
    URC_CT037 --> AT303
    URC_CT037 --> AT304
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT037 componentStyle
    class AT297,AT298,AT299,AT300,AT301,AT302,AT303,AT304 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>