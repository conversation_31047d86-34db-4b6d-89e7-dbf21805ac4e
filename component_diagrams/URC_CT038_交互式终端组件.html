<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT038 交互式终端组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT038 交互式终端组件</h1>
            <p><strong>功能分类：</strong>命令执行 (Command Execution)</p>
            <p><strong>组件描述：</strong>伪终端会话、实时交互和会话管理</p>
            <p><strong>原子函数范围：</strong>URC_AT305-URC_AT312</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[命令执行<br/>Command Execution]
    
    %% 组件
    URC_CT038[URC_CT038<br/>交互式终端组件<br/>伪终端会话、实时交互和会话管理]
    
    %% 原子函数
    AT305[URC_AT305<br/>创建PTY会话<br/>创建伪终端会话]
    AT306[URC_AT306<br/>终端属性设置<br/>设置终端属性和模式]
    AT307[URC_AT307<br/>实时输入处理<br/>处理实时键盘输入]
    AT308[URC_AT308<br/>实时输出捕获<br/>捕获终端实时输出]
    AT309[URC_AT309<br/>终端大小调整<br/>调整终端窗口大小]
    AT310[URC_AT310<br/>会话状态管理<br/>管理终端会话状态]
    AT311[URC_AT311<br/>终端历史记录<br/>记录终端会话历史]
    AT312[URC_AT312<br/>会话恢复<br/>恢复之前的终端会话]
    
    %% 关系连接
    CAT --> URC_CT038
    URC_CT038 --> AT305
    URC_CT038 --> AT306
    URC_CT038 --> AT307
    URC_CT038 --> AT308
    URC_CT038 --> AT309
    URC_CT038 --> AT310
    URC_CT038 --> AT311
    URC_CT038 --> AT312
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT038 componentStyle
    class AT305,AT306,AT307,AT308,AT309,AT310,AT311,AT312 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>