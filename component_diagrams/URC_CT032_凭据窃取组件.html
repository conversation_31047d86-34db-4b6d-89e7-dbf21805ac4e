<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT032 凭据窃取组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT032 凭据窃取组件</h1>
            <p><strong>功能分类：</strong>横向移动 (Lateral Movement)</p>
            <p><strong>组件描述：</strong>凭据获取、密码提取和认证信息收集</p>
            <p><strong>原子函数范围：</strong>URC_AT257-URC_AT264</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[横向移动<br/>Lateral Movement]
    
    %% 组件
    URC_CT032[URC_CT032<br/>凭据窃取组件<br/>凭据获取、密码提取和认证信息收集]
    
    %% 原子函数
    AT257[URC_AT257<br/>内存凭据提取<br/>从内存中提取凭据]
    AT258[URC_AT258<br/>浏览器密码<br/>提取浏览器保存的密码]
    AT259[URC_AT259<br/>系统凭据<br/>提取系统存储的凭据]
    AT260[URC_AT260<br/>哈希提取<br/>提取密码哈希值]
    AT261[URC_AT261<br/>票据窃取<br/>窃取Kerberos票据]
    AT262[URC_AT262<br/>令牌复制<br/>复制访问令牌]
    AT263[URC_AT263<br/>凭据缓存<br/>缓存获取的凭据]
    AT264[URC_AT264<br/>凭据验证<br/>验证凭据有效性]
    
    %% 关系连接
    CAT --> URC_CT032
    URC_CT032 --> AT257
    URC_CT032 --> AT258
    URC_CT032 --> AT259
    URC_CT032 --> AT260
    URC_CT032 --> AT261
    URC_CT032 --> AT262
    URC_CT032 --> AT263
    URC_CT032 --> AT264
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT032 componentStyle
    class AT257,AT258,AT259,AT260,AT261,AT262,AT263,AT264 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>