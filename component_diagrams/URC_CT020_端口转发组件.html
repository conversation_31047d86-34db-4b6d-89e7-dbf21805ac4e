<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT020 端口转发组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT020 端口转发组件</h1>
            <p><strong>功能分类：</strong>网络代理 (Network Proxy)</p>
            <p><strong>组件描述：</strong>端口转发、流量重定向和连接管理</p>
            <p><strong>原子函数范围：</strong>URC_AT161-URC_AT168</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[网络代理<br/>Network Proxy]
    
    %% 组件
    URC_CT020[URC_CT020<br/>端口转发组件<br/>端口转发、流量重定向和连接管理]
    
    %% 原子函数
    AT161[URC_AT161<br/>创建端口转发<br/>创建端口转发规则]
    AT162[URC_AT162<br/>启动端口转发<br/>启动端口转发服务]
    AT163[URC_AT163<br/>停止端口转发<br/>停止端口转发服务]
    AT164[URC_AT164<br/>转发连接<br/>转发客户端连接到目标服务器]
    AT165[URC_AT165<br/>列出活跃转发<br/>列出所有活跃的端口转发]
    AT166[URC_AT166<br/>获取转发统计<br/>获取端口转发统计信息]
    AT167[URC_AT167<br/>处理转发错误<br/>处理端口转发错误]
    AT168[URC_AT168<br/>清理转发资源<br/>清理端口转发相关资源]
    
    %% 关系连接
    CAT --> URC_CT020
    URC_CT020 --> AT161
    URC_CT020 --> AT162
    URC_CT020 --> AT163
    URC_CT020 --> AT164
    URC_CT020 --> AT165
    URC_CT020 --> AT166
    URC_CT020 --> AT167
    URC_CT020 --> AT168
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT020 componentStyle
    class AT161,AT162,AT163,AT164,AT165,AT166,AT167,AT168 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>