<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT018 心跳监控组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT018 心跳监控组件</h1>
            <p><strong>功能分类：</strong>监控日志 (Monitoring Logging)</p>
            <p><strong>组件描述：</strong>连接状态监控、心跳检测和超时处理</p>
            <p><strong>原子函数范围：</strong>URC_AT025-URC_AT032</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[监控日志<br/>Monitoring Logging]
    
    %% 组件
    URC_CT018[URC_CT018<br/>心跳监控组件<br/>连接状态监控、心跳检测和超时处理]
    
    %% 原子函数
    AT025[URC_AT025<br/>心跳监控启动<br/>启动心跳监控线程]
    AT026[URC_AT026<br/>发送心跳<br/>向客户端发送心跳包]
    AT027[URC_AT027<br/>接收心跳<br/>接收客户端心跳响应]
    AT028[URC_AT028<br/>客户端超时检查<br/>检查客户端心跳超时]
    AT029[URC_AT029<br/>更新最后心跳时间<br/>更新客户端最后心跳时间]
    AT030[URC_AT030<br/>设置心跳间隔<br/>设置心跳检测间隔]
    AT031[URC_AT031<br/>处理心跳超时<br/>处理客户端心跳超时]
    AT032[URC_AT032<br/>停止心跳监控<br/>停止心跳监控线程]
    
    %% 关系连接
    CAT --> URC_CT018
    URC_CT018 --> AT025
    URC_CT018 --> AT026
    URC_CT018 --> AT027
    URC_CT018 --> AT028
    URC_CT018 --> AT029
    URC_CT018 --> AT030
    URC_CT018 --> AT031
    URC_CT018 --> AT032
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT018 componentStyle
    class AT025,AT026,AT027,AT028,AT029,AT030,AT031,AT032 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>