<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT033 远程执行组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT033 远程执行组件</h1>
            <p><strong>功能分类：</strong>横向移动 (Lateral Movement)</p>
            <p><strong>组件描述：</strong>远程代码执行、载荷投递和控制</p>
            <p><strong>原子函数范围：</strong>URC_AT265-URC_AT272</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[横向移动<br/>Lateral Movement]
    
    %% 组件
    URC_CT033[URC_CT033<br/>远程执行组件<br/>远程代码执行、载荷投递和控制]
    
    %% 原子函数
    AT265[URC_AT265<br/>WMI执行<br/>通过WMI执行远程命令]
    AT266[URC_AT266<br/>SSH执行<br/>通过SSH执行远程命令]
    AT267[URC_AT267<br/>PsExec执行<br/>通过PsExec执行远程命令]
    AT268[URC_AT268<br/>载荷投递<br/>投递执行载荷到目标主机]
    AT269[URC_AT269<br/>远程服务创建<br/>在远程主机创建服务]
    AT270[URC_AT270<br/>计划任务执行<br/>通过计划任务执行]
    AT271[URC_AT271<br/>漏洞利用执行<br/>通过漏洞利用执行代码]
    AT272[URC_AT272<br/>建立持久化<br/>在目标主机建立持久化]
    
    %% 关系连接
    CAT --> URC_CT033
    URC_CT033 --> AT265
    URC_CT033 --> AT266
    URC_CT033 --> AT267
    URC_CT033 --> AT268
    URC_CT033 --> AT269
    URC_CT033 --> AT270
    URC_CT033 --> AT271
    URC_CT033 --> AT272
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT033 componentStyle
    class AT265,AT266,AT267,AT268,AT269,AT270,AT271,AT272 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>