<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT027 注册表操作组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT027 注册表操作组件</h1>
            <p><strong>功能分类：</strong>系统控制 (System Control)</p>
            <p><strong>组件描述：</strong>注册表操作、配置修改和系统设置</p>
            <p><strong>原子函数范围：</strong>URC_AT217-URC_AT224</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[系统控制<br/>System Control]
    
    %% 组件
    URC_CT027[URC_CT027<br/>注册表操作组件<br/>注册表操作、配置修改和系统设置]
    
    %% 原子函数
    AT217[URC_AT217<br/>读取注册表<br/>读取注册表键值]
    AT218[URC_AT218<br/>写入注册表<br/>写入注册表键值]
    AT219[URC_AT219<br/>删除注册表项<br/>删除注册表项]
    AT220[URC_AT220<br/>枚举注册表<br/>枚举注册表子项]
    AT221[URC_AT221<br/>备份注册表<br/>备份注册表分支]
    AT222[URC_AT222<br/>恢复注册表<br/>恢复注册表备份]
    AT223[URC_AT223<br/>监控注册表变化<br/>监控注册表变化]
    AT224[URC_AT224<br/>注册表权限设置<br/>设置注册表项权限]
    
    %% 关系连接
    CAT --> URC_CT027
    URC_CT027 --> AT217
    URC_CT027 --> AT218
    URC_CT027 --> AT219
    URC_CT027 --> AT220
    URC_CT027 --> AT221
    URC_CT027 --> AT222
    URC_CT027 --> AT223
    URC_CT027 --> AT224
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT027 componentStyle
    class AT217,AT218,AT219,AT220,AT221,AT222,AT223,AT224 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>