<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT035 数据压缩组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT035 数据压缩组件</h1>
            <p><strong>功能分类：</strong>数据外泄 (Data Exfiltration)</p>
            <p><strong>组件描述：</strong>数据压缩、打包和格式转换</p>
            <p><strong>原子函数范围：</strong>URC_AT281-URC_AT288</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[数据外泄<br/>Data Exfiltration]
    
    %% 组件
    URC_CT035[URC_CT035<br/>数据压缩组件<br/>数据压缩、打包和格式转换]
    
    %% 原子函数
    AT281[URC_AT281<br/>文件压缩<br/>压缩文件和目录]
    AT282[URC_AT282<br/>数据打包<br/>打包多个文件]
    AT283[URC_AT283<br/>加密压缩<br/>压缩并加密数据]
    AT284[URC_AT284<br/>分卷压缩<br/>分卷压缩大文件]
    AT285[URC_AT285<br/>压缩率优化<br/>优化压缩率]
    AT286[URC_AT286<br/>解压缩<br/>解压缩文件]
    AT287[URC_AT287<br/>压缩验证<br/>验证压缩文件完整性]
    AT288[URC_AT288<br/>压缩格式转换<br/>转换压缩文件格式]
    
    %% 关系连接
    CAT --> URC_CT035
    URC_CT035 --> AT281
    URC_CT035 --> AT282
    URC_CT035 --> AT283
    URC_CT035 --> AT284
    URC_CT035 --> AT285
    URC_CT035 --> AT286
    URC_CT035 --> AT287
    URC_CT035 --> AT288
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT035 componentStyle
    class AT281,AT282,AT283,AT284,AT285,AT286,AT287,AT288 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>