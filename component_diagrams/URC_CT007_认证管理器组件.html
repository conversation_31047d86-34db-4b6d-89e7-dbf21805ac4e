<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT007 认证管理器组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT007 认证管理器组件</h1>
            <p><strong>功能分类：</strong>认证安全 (Authentication Security)</p>
            <p><strong>组件描述：</strong>用户认证、权限管理和访问控制</p>
            <p><strong>原子函数范围：</strong>URC_AT041-URC_AT048</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[认证安全<br/>Authentication Security]
    
    %% 组件
    URC_CT007[URC_CT007<br/>认证管理器组件<br/>用户认证、权限管理和访问控制]
    
    %% 原子函数
    AT041[URC_AT041<br/>生成认证令牌<br/>生成客户端认证令牌]
    AT042[URC_AT042<br/>验证认证令牌<br/>验证客户端提供的令牌]
    AT043[URC_AT043<br/>客户端认证<br/>执行客户端身份认证]
    AT044[URC_AT044<br/>操作授权<br/>检查操作权限]
    AT045[URC_AT045<br/>刷新认证令牌<br/>刷新过期的认证令牌]
    AT046[URC_AT046<br/>撤销认证令牌<br/>撤销指定的认证令牌]
    AT047[URC_AT047<br/>加密凭据<br/>加密用户凭据信息]
    AT048[URC_AT048<br/>解密凭据<br/>解密用户凭据信息]
    
    %% 关系连接
    CAT --> URC_CT007
    URC_CT007 --> AT041
    URC_CT007 --> AT042
    URC_CT007 --> AT043
    URC_CT007 --> AT044
    URC_CT007 --> AT045
    URC_CT007 --> AT046
    URC_CT007 --> AT047
    URC_CT007 --> AT048
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT007 componentStyle
    class AT041,AT042,AT043,AT044,AT045,AT046,AT047,AT048 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>