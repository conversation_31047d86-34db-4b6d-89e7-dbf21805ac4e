<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT013 系统信息收集器组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT013 系统信息收集器组件</h1>
            <p><strong>功能分类：</strong>系统监控 (System Monitoring)</p>
            <p><strong>组件描述：</strong>系统信息获取、硬件检测和状态监控</p>
            <p><strong>原子函数范围：</strong>URC_AT097-URC_AT104</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[系统监控<br/>System Monitoring]
    
    %% 组件
    URC_CT013[URC_CT013<br/>系统信息收集器组件<br/>系统信息获取、硬件检测和状态监控]
    
    %% 原子函数
    AT097[URC_AT097<br/>获取系统信息<br/>获取基本系统信息]
    AT098[URC_AT098<br/>获取硬件信息<br/>获取硬件配置信息]
    AT099[URC_AT099<br/>获取网络信息<br/>获取网络配置信息]
    AT100[URC_AT100<br/>获取进程列表<br/>获取当前运行进程列表]
    AT101[URC_AT101<br/>获取内存使用<br/>获取内存使用情况]
    AT102[URC_AT102<br/>获取CPU使用<br/>获取CPU使用情况]
    AT103[URC_AT103<br/>获取磁盘使用<br/>获取磁盘使用情况]
    AT104[URC_AT104<br/>获取系统运行时间<br/>获取系统运行时间]
    
    %% 关系连接
    CAT --> URC_CT013
    URC_CT013 --> AT097
    URC_CT013 --> AT098
    URC_CT013 --> AT099
    URC_CT013 --> AT100
    URC_CT013 --> AT101
    URC_CT013 --> AT102
    URC_CT013 --> AT103
    URC_CT013 --> AT104
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT013 componentStyle
    class AT097,AT098,AT099,AT100,AT101,AT102,AT103,AT104 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>