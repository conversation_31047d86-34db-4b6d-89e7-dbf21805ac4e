<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT026 系统监控组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT026 系统监控组件</h1>
            <p><strong>功能分类：</strong>系统控制 (System Control)</p>
            <p><strong>组件描述：</strong>系统监控、事件检测和状态跟踪</p>
            <p><strong>原子函数范围：</strong>URC_AT209-URC_AT216</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[系统控制<br/>System Control]
    
    %% 组件
    URC_CT026[URC_CT026<br/>系统监控组件<br/>系统监控、事件检测和状态跟踪]
    
    %% 原子函数
    AT209[URC_AT209<br/>文件系统监控<br/>监控文件系统变化]
    AT210[URC_AT210<br/>注册表监控<br/>监控注册表变化]
    AT211[URC_AT211<br/>进程监控<br/>监控进程创建和终止]
    AT212[URC_AT212<br/>网络监控<br/>监控网络连接活动]
    AT213[URC_AT213<br/>服务监控<br/>监控系统服务状态]
    AT214[URC_AT214<br/>用户活动监控<br/>监控用户登录活动]
    AT215[URC_AT215<br/>系统事件监控<br/>监控系统事件日志]
    AT216[URC_AT216<br/>性能监控<br/>监控系统性能指标]
    
    %% 关系连接
    CAT --> URC_CT026
    URC_CT026 --> AT209
    URC_CT026 --> AT210
    URC_CT026 --> AT211
    URC_CT026 --> AT212
    URC_CT026 --> AT213
    URC_CT026 --> AT214
    URC_CT026 --> AT215
    URC_CT026 --> AT216
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT026 componentStyle
    class AT209,AT210,AT211,AT212,AT213,AT214,AT215,AT216 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>