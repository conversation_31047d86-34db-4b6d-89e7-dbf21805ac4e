<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT036 隐蔽传输组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT036 隐蔽传输组件</h1>
            <p><strong>功能分类：</strong>数据外泄 (Data Exfiltration)</p>
            <p><strong>组件描述：</strong>隐蔽数据传输、通道建立和检测规避</p>
            <p><strong>原子函数范围：</strong>URC_AT289-URC_AT296</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[数据外泄<br/>Data Exfiltration]
    
    %% 组件
    URC_CT036[URC_CT036<br/>隐蔽传输组件<br/>隐蔽数据传输、通道建立和检测规避]
    
    %% 原子函数
    AT289[URC_AT289<br/>邮件传输<br/>通过邮件传输数据]
    AT290[URC_AT290<br/>DNS传输<br/>通过DNS查询传输数据]
    AT291[URC_AT291<br/>HTTPS传输<br/>通过HTTPS传输数据]
    AT292[URC_AT292<br/>云存储传输<br/>通过云存储服务传输]
    AT293[URC_AT293<br/>社交媒体传输<br/>通过社交媒体传输]
    AT294[URC_AT294<br/>P2P传输<br/>通过P2P网络传输]
    AT295[URC_AT295<br/>USB传输<br/>通过USB设备传输]
    AT296[URC_AT296<br/>打印机传输<br/>通过网络打印机传输]
    
    %% 关系连接
    CAT --> URC_CT036
    URC_CT036 --> AT289
    URC_CT036 --> AT290
    URC_CT036 --> AT291
    URC_CT036 --> AT292
    URC_CT036 --> AT293
    URC_CT036 --> AT294
    URC_CT036 --> AT295
    URC_CT036 --> AT296
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT036 componentStyle
    class AT289,AT290,AT291,AT292,AT293,AT294,AT295,AT296 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>