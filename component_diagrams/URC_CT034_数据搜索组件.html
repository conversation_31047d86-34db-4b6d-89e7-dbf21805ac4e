<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT034 数据搜索组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT034 数据搜索组件</h1>
            <p><strong>功能分类：</strong>数据外泄 (Data Exfiltration)</p>
            <p><strong>组件描述：</strong>敏感数据搜索、文件扫描和内容分析</p>
            <p><strong>原子函数范围：</strong>URC_AT273-URC_AT280</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[数据外泄<br/>Data Exfiltration]
    
    %% 组件
    URC_CT034[URC_CT034<br/>数据搜索组件<br/>敏感数据搜索、文件扫描和内容分析]
    
    %% 原子函数
    AT273[URC_AT273<br/>文件搜索<br/>搜索指定类型的文件]
    AT274[URC_AT274<br/>内容搜索<br/>搜索文件内容中的关键词]
    AT275[URC_AT275<br/>敏感数据识别<br/>识别敏感数据模式]
    AT276[URC_AT276<br/>数据库搜索<br/>搜索数据库中的数据]
    AT277[URC_AT277<br/>邮件搜索<br/>搜索邮件内容]
    AT278[URC_AT278<br/>注册表搜索<br/>搜索注册表中的信息]
    AT279[URC_AT279<br/>内存搜索<br/>搜索内存中的数据]
    AT280[URC_AT280<br/>搜索结果过滤<br/>过滤和排序搜索结果]
    
    %% 关系连接
    CAT --> URC_CT034
    URC_CT034 --> AT273
    URC_CT034 --> AT274
    URC_CT034 --> AT275
    URC_CT034 --> AT276
    URC_CT034 --> AT277
    URC_CT034 --> AT278
    URC_CT034 --> AT279
    URC_CT034 --> AT280
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT034 componentStyle
    class AT273,AT274,AT275,AT276,AT277,AT278,AT279,AT280 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>