<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT008 加密解密组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT008 加密解密组件</h1>
            <p><strong>功能分类：</strong>认证安全 (Authentication Security)</p>
            <p><strong>组件描述：</strong>数据加密、密钥管理和安全算法</p>
            <p><strong>原子函数范围：</strong>URC_AT121-URC_AT128</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[认证安全<br/>Authentication Security]
    
    %% 组件
    URC_CT008[URC_CT008<br/>加密解密组件<br/>数据加密、密钥管理和安全算法]
    
    %% 原子函数
    AT121[URC_AT121<br/>生成加密密钥<br/>生成加密密钥]
    AT122[URC_AT122<br/>AES加密<br/>使用AES算法加密数据]
    AT123[URC_AT123<br/>AES解密<br/>使用AES算法解密数据]
    AT124[URC_AT124<br/>生成哈希<br/>生成数据哈希值]
    AT125[URC_AT125<br/>验证哈希<br/>验证数据哈希值]
    AT126[URC_AT126<br/>生成随机字节<br/>生成安全随机字节]
    AT127[URC_AT127<br/>密码派生密钥<br/>从密码派生加密密钥]
    AT128[URC_AT128<br/>安全内存擦除<br/>安全擦除敏感内存数据]
    
    %% 关系连接
    CAT --> URC_CT008
    URC_CT008 --> AT121
    URC_CT008 --> AT122
    URC_CT008 --> AT123
    URC_CT008 --> AT124
    URC_CT008 --> AT125
    URC_CT008 --> AT126
    URC_CT008 --> AT127
    URC_CT008 --> AT128
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT008 componentStyle
    class AT121,AT122,AT123,AT124,AT125,AT126,AT127,AT128 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>