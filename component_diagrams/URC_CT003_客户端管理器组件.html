<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT003 客户端管理器组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT003 客户端管理器组件</h1>
            <p><strong>功能分类：</strong>核心架构 (Core Architecture)</p>
            <p><strong>组件描述：</strong>多客户端连接管理、会话状态和连接池</p>
            <p><strong>原子函数范围：</strong>URC_AT017-URC_AT024</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[核心架构<br/>Core Architecture]
    
    %% 组件
    URC_CT003[URC_CT003<br/>客户端管理器组件<br/>多客户端连接管理、会话状态和连接池]
    
    %% 原子函数
    AT017[URC_AT017<br/>客户端添加<br/>添加新客户端到管理列表]
    AT018[URC_AT018<br/>客户端移除<br/>从管理列表中移除客户端]
    AT019[URC_AT019<br/>客户端查找<br/>根据ID查找客户端]
    AT020[URC_AT020<br/>活跃客户端列表<br/>获取所有活跃客户端列表]
    AT021[URC_AT021<br/>客户端状态更新<br/>更新客户端状态信息]
    AT022[URC_AT022<br/>客户端超时检查<br/>检查客户端是否超时]
    AT023[URC_AT023<br/>广播消息<br/>向所有客户端广播消息]
    AT024[URC_AT024<br/>客户端计数<br/>获取当前客户端数量]
    
    %% 关系连接
    CAT --> URC_CT003
    URC_CT003 --> AT017
    URC_CT003 --> AT018
    URC_CT003 --> AT019
    URC_CT003 --> AT020
    URC_CT003 --> AT021
    URC_CT003 --> AT022
    URC_CT003 --> AT023
    URC_CT003 --> AT024
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT003 componentStyle
    class AT017,AT018,AT019,AT020,AT021,AT022,AT023,AT024 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>