<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT040 热升级组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT040 热升级组件</h1>
            <p><strong>功能分类：</strong>系统控制 (System Control)</p>
            <p><strong>组件描述：</strong>在线升级、版本管理和热替换</p>
            <p><strong>原子函数范围：</strong>URC_AT321-URC_AT328</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[系统控制<br/>System Control]
    
    %% 组件
    URC_CT040[URC_CT040<br/>热升级组件<br/>在线升级、版本管理和热替换]
    
    %% 原子函数
    AT321[URC_AT321<br/>版本检测<br/>检测当前程序版本信息]
    AT322[URC_AT322<br/>升级包验证<br/>验证升级包的完整性和签名]
    AT323[URC_AT323<br/>备份当前版本<br/>备份当前运行的程序版本]
    AT324[URC_AT324<br/>热替换执行<br/>执行热替换操作]
    AT325[URC_AT325<br/>配置迁移<br/>迁移配置文件到新版本]
    AT326[URC_AT326<br/>服务重启<br/>重启系统服务]
    AT327[URC_AT327<br/>回滚机制<br/>回滚到之前的版本]
    AT328[URC_AT328<br/>升级状态报告<br/>生成升级状态报告]
    
    %% 关系连接
    CAT --> URC_CT040
    URC_CT040 --> AT321
    URC_CT040 --> AT322
    URC_CT040 --> AT323
    URC_CT040 --> AT324
    URC_CT040 --> AT325
    URC_CT040 --> AT326
    URC_CT040 --> AT327
    URC_CT040 --> AT328
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT040 componentStyle
    class AT321,AT322,AT323,AT324,AT325,AT326,AT327,AT328 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>