<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT024 网络隐蔽组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT024 网络隐蔽组件</h1>
            <p><strong>功能分类：</strong>高级安全 (Advanced Security)</p>
            <p><strong>组件描述：</strong>网络流量隐蔽、协议伪装和检测规避</p>
            <p><strong>原子函数范围：</strong>URC_AT193-URC_AT200</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[高级安全<br/>Advanced Security]
    
    %% 组件
    URC_CT024[URC_CT024<br/>网络隐蔽组件<br/>网络流量隐蔽、协议伪装和检测规避]
    
    %% 原子函数
    AT193[URC_AT193<br/>流量加密<br/>加密网络通信流量]
    AT194[URC_AT194<br/>协议伪装<br/>伪装成合法协议]
    AT195[URC_AT195<br/>域名生成<br/>动态生成通信域名]
    AT196[URC_AT196<br/>代理链<br/>建立代理链隐藏真实IP]
    AT197[URC_AT197<br/>流量混淆<br/>混淆网络流量特征]
    AT198[URC_AT198<br/>时间随机化<br/>随机化通信时间间隔]
    AT199[URC_AT199<br/>分片传输<br/>分片传输数据包]
    AT200[URC_AT200<br/>隐蔽通道<br/>建立隐蔽通信通道]
    
    %% 关系连接
    CAT --> URC_CT024
    URC_CT024 --> AT193
    URC_CT024 --> AT194
    URC_CT024 --> AT195
    URC_CT024 --> AT196
    URC_CT024 --> AT197
    URC_CT024 --> AT198
    URC_CT024 --> AT199
    URC_CT024 --> AT200
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT024 componentStyle
    class AT193,AT194,AT195,AT196,AT197,AT198,AT199,AT200 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>