<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT042 持久化增强组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT042 持久化增强组件</h1>
            <p><strong>功能分类：</strong>高级安全 (Advanced Security)</p>
            <p><strong>组件描述：</strong>增强持久化、故障恢复和状态同步</p>
            <p><strong>原子函数范围：</strong>URC_AT337-URC_AT344</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[高级安全<br/>Advanced Security]
    
    %% 组件
    URC_CT042[URC_CT042<br/>持久化增强组件<br/>增强持久化、故障恢复和状态同步]
    
    %% 原子函数
    AT337[URC_AT337<br/>配置状态保存<br/>保存当前配置状态]
    AT338[URC_AT338<br/>连接状态恢复<br/>恢复之前的连接状态]
    AT339[URC_AT339<br/>服务监控<br/>监控服务运行状态]
    AT340[URC_AT340<br/>自动重启<br/>自动重启服务]
    AT341[URC_AT341<br/>故障检测<br/>检测服务故障]
    AT342[URC_AT342<br/>恢复策略<br/>执行故障恢复策略]
    AT343[URC_AT343<br/>状态同步<br/>同步本地和远程状态]
    AT344[URC_AT344<br/>持久化验证<br/>验证持久化机制]
    
    %% 关系连接
    CAT --> URC_CT042
    URC_CT042 --> AT337
    URC_CT042 --> AT338
    URC_CT042 --> AT339
    URC_CT042 --> AT340
    URC_CT042 --> AT341
    URC_CT042 --> AT342
    URC_CT042 --> AT343
    URC_CT042 --> AT344
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT042 componentStyle
    class AT337,AT338,AT339,AT340,AT341,AT342,AT343,AT344 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>