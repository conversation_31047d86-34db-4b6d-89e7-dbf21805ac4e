<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT030 网络嗅探组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT030 网络嗅探组件</h1>
            <p><strong>功能分类：</strong>数据收集 (Data Collection)</p>
            <p><strong>组件描述：</strong>网络数据包捕获、协议分析和流量监控</p>
            <p><strong>原子函数范围：</strong>URC_AT241-URC_AT248</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[数据收集<br/>Data Collection]
    
    %% 组件
    URC_CT030[URC_CT030<br/>网络嗅探组件<br/>网络数据包捕获、协议分析和流量监控]
    
    %% 原子函数
    AT241[URC_AT241<br/>网卡混杂模式<br/>设置网卡为混杂模式]
    AT242[URC_AT242<br/>数据包捕获<br/>捕获网络数据包]
    AT243[URC_AT243<br/>协议解析<br/>解析网络协议]
    AT244[URC_AT244<br/>流量过滤<br/>过滤特定网络流量]
    AT245[URC_AT245<br/>数据包重组<br/>重组分片数据包]
    AT246[URC_AT246<br/>会话重建<br/>重建网络会话]
    AT247[URC_AT247<br/>敏感信息提取<br/>提取敏感信息]
    AT248[URC_AT248<br/>流量统计<br/>统计网络流量信息]
    
    %% 关系连接
    CAT --> URC_CT030
    URC_CT030 --> AT241
    URC_CT030 --> AT242
    URC_CT030 --> AT243
    URC_CT030 --> AT244
    URC_CT030 --> AT245
    URC_CT030 --> AT246
    URC_CT030 --> AT247
    URC_CT030 --> AT248
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT030 componentStyle
    class AT241,AT242,AT243,AT244,AT245,AT246,AT247,AT248 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>