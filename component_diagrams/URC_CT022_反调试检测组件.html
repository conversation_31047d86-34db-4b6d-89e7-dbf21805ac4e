<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URC_CT022 反调试检测组件 - 详细关系图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            border-left: 4px solid #4a148c;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #4a148c;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a148c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #6a1b9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URC_CT022 反调试检测组件</h1>
            <p><strong>功能分类：</strong>高级安全 (Advanced Security)</p>
            <p><strong>组件描述：</strong>调试检测、反分析和保护机制</p>
            <p><strong>原子函数范围：</strong>URC_AT177-URC_AT184</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph LR
    %% 功能分类
    CAT[高级安全<br/>Advanced Security]
    
    %% 组件
    URC_CT022[URC_CT022<br/>反调试检测组件<br/>调试检测、反分析和保护机制]
    
    %% 原子函数
    AT177[URC_AT177<br/>检测调试器<br/>检测是否有调试器附加]
    AT178[URC_AT178<br/>反虚拟机检测<br/>检测是否运行在虚拟机中]
    AT179[URC_AT179<br/>代码混淆<br/>对关键代码进行混淆]
    AT180[URC_AT180<br/>反汇编检测<br/>检测反汇编工具]
    AT181[URC_AT181<br/>时间检测<br/>通过时间差检测调试]
    AT182[URC_AT182<br/>异常处理<br/>设置反调试异常处理]
    AT183[URC_AT183<br/>进程检测<br/>检测调试相关进程]
    AT184[URC_AT184<br/>自我保护<br/>启用程序自我保护机制]
    
    %% 关系连接
    CAT --> URC_CT022
    URC_CT022 --> AT177
    URC_CT022 --> AT178
    URC_CT022 --> AT179
    URC_CT022 --> AT180
    URC_CT022 --> AT181
    URC_CT022 --> AT182
    URC_CT022 --> AT183
    URC_CT022 --> AT184
    
    %% 样式定义
    classDef categoryStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000,font-size:14px
    classDef componentStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000,font-size:12px
    classDef atomicStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,color:#000,font-size:10px
    
    %% 应用样式
    class CAT categoryStyle
    class URC_CT022 componentStyle
    class AT177,AT178,AT179,AT180,AT181,AT182,AT183,AT184 atomicStyle
            </div>
        </div>
        
        <div class="info-panel">
            <h3>图表说明</h3>
            <ul>
                <li><strong>蓝色节点：</strong>功能分类</li>
                <li><strong>紫色节点：</strong>组件</li>
                <li><strong>绿色节点：</strong>原子函数</li>
                <li><strong>布局方向：</strong>从左到右</li>
            </ul>
        </div>
        
        <a href="components_index.html" class="back-link">← 返回组件列表</a>
    </div>
    
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>