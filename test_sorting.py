#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel文件中的ID排序是否正确
"""

import pandas as pd
import re

def extract_number_from_id(id_str):
    """从ID字符串中提取数字部分用于排序"""
    match = re.search(r'(\d+)', id_str)
    return int(match.group(1)) if match else 0

def test_sorting():
    """测试Excel文件中的排序"""
    excel_file = "系统架构表.xlsx"
    
    try:
        # 读取Excel文件
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        print("测试Excel文件排序...")
        print("=" * 50)
        
        # 测试原子表排序
        if "原子表" in excel_data:
            atomic_df = excel_data["原子表"]
            atomic_ids = atomic_df['原子功能ID'].tolist()
            
            print("原子表ID排序测试：")
            print(f"前10个ID: {atomic_ids[:10]}")
            print(f"后10个ID: {atomic_ids[-10:]}")
            
            # 检查是否按数字顺序排列
            numbers = [extract_number_from_id(id_str) for id_str in atomic_ids]
            is_sorted = all(numbers[i] <= numbers[i+1] for i in range(len(numbers)-1))
            print(f"原子功能ID排序正确: {'✓' if is_sorted else '✗'}")
            
            if not is_sorted:
                # 找出不正确的位置
                for i in range(len(numbers)-1):
                    if numbers[i] > numbers[i+1]:
                        print(f"  排序错误位置: {atomic_ids[i]} -> {atomic_ids[i+1]}")
                        break
            print()
        
        # 测试组件表排序
        if "组件表" in excel_data:
            component_df = excel_data["组件表"]
            component_ids = component_df['组件ID'].tolist()
            
            print("组件表ID排序测试：")
            print(f"前10个ID: {component_ids[:10]}")
            print(f"后10个ID: {component_ids[-10:]}")
            
            # 检查是否按数字顺序排列
            numbers = [extract_number_from_id(id_str) for id_str in component_ids]
            is_sorted = all(numbers[i] <= numbers[i+1] for i in range(len(numbers)-1))
            print(f"组件ID排序正确: {'✓' if is_sorted else '✗'}")
            
            if not is_sorted:
                # 找出不正确的位置
                for i in range(len(numbers)-1):
                    if numbers[i] > numbers[i+1]:
                        print(f"  排序错误位置: {component_ids[i]} -> {component_ids[i+1]}")
                        break
            print()
        
        print("排序测试完成！")
        
    except Exception as e:
        print(f"测试过程中出错：{e}")

if __name__ == "__main__":
    test_sorting()
